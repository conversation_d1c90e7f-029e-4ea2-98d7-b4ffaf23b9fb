time="2025-06-27 18:22:54" level=info msg="日志输出到文件: data_sync.log"
time="2025-06-27 18:22:54" level=info msg="启动 api2mysql v1.0.0"
time="2025-06-27 18:22:54" level=info msg="使用配置文件: config-production.yaml"
time="2025-06-27 18:22:54" level=info msg="执行一次性同步: department_basic"
time="2025-06-27 18:22:54" level=info msg="创建表: CREATE TABLE `department_basic` (`department_short_name` VARCHAR(100) COMMENT '单位简称', `parent_department_code` VARCHAR(20) COMMENT '隶属单位号', `parent_department_name` VARCHAR(200) COMMENT '隶属单位名称', `department_validity_flag` VARCHAR(10) COMMENT '单位有效标识', `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间', `department_name` VARCHAR(200) NOT NULL COMMENT '单位名称', `department_english_name` VARCHAR(200) COMMENT '单位英文名称', `department_head_name` VARCHAR(100) COMMENT '单位负责人名称', `effective_date` DATE COMMENT '生效日期', `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间', `department_code` VARCHAR(20) PRIMARY KEY COMMENT '单位号', `department_head_code` VARCHAR(20) COMMENT '单位负责人号') ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
time="2025-06-27 18:22:54" level=info msg="表 department_basic 创建成功"
time="2025-06-27 18:22:54" level=info msg="开始同步API数据: department_basic"
time="2025-06-27 18:22:54" level=info msg="表 department_basic 已存在"
time="2025-06-27 18:22:54" level=info msg="开始获取API数据: department_basic"
time="2025-06-27 18:22:54" level=info msg="成功获取API数据: department_basic，共 118 条记录"
time="2025-06-27 18:22:54" level=info msg="数据转换完成，共处理 118 条记录"
time="2025-06-27 18:22:54" level=error msg="数据同步到数据库失败 [department_basic]: 更新插入数据失败: Error 1292: Incorrect date value: '' for column 'effective_date' at row 1"
time="2025-06-27 18:22:54" level=info msg="一次性同步完成: department_basic"
time="2025-06-27 18:31:05" level=info msg="日志输出到文件: data_sync.log"
time="2025-06-27 18:31:05" level=info msg="启动 api2mysql v1.0.0"
time="2025-06-27 18:31:05" level=info msg="使用配置文件: config-production.yaml"
time="2025-06-27 18:31:05" level=info msg="执行一次性同步: department_basic"
time="2025-06-27 18:31:05" level=info msg="创建表: CREATE TABLE `department_basic` (`department_validity_flag` VARCHAR(10) COMMENT '单位有效标识', `department_name` VARCHAR(200) NOT NULL COMMENT '单位名称', `department_english_name` VARCHAR(200) COMMENT '单位英文名称', `department_short_name` VARCHAR(100) COMMENT '单位简称', `parent_department_name` VARCHAR(200) COMMENT '隶属单位名称', `department_head_code` VARCHAR(20) COMMENT '单位负责人号', `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间', `department_code` VARCHAR(20) PRIMARY KEY COMMENT '单位号', `parent_department_code` VARCHAR(20) COMMENT '隶属单位号', `department_head_name` VARCHAR(100) COMMENT '单位负责人名称', `effective_date` VARCHAR(20) COMMENT '生效日期', `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间') ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
time="2025-06-27 18:31:05" level=info msg="表 department_basic 创建成功"
time="2025-06-27 18:31:05" level=info msg="开始同步API数据: department_basic"
time="2025-06-27 18:31:05" level=info msg="表 department_basic 已存在"
time="2025-06-27 18:31:05" level=info msg="开始获取API数据: department_basic"
time="2025-06-27 18:31:05" level=info msg="成功获取API数据: department_basic，共 118 条记录"
time="2025-06-27 18:31:05" level=info msg="数据转换完成，共处理 118 条记录"
time="2025-06-27 18:31:05" level=info msg="成功更新插入 118 条数据到表 department_basic"
time="2025-06-27 18:31:05" level=info msg="API数据同步完成: department_basic，耗时: 294.355083ms，记录数: 118"
time="2025-06-27 18:31:05" level=info msg="一次性同步完成: department_basic"
time="2025-06-27 18:44:02" level=info msg="日志输出到文件: data_sync.log"
time="2025-06-27 18:44:02" level=info msg="启动 api2mysql v1.0.0"
time="2025-06-27 18:44:02" level=info msg="使用配置文件: config-production.yaml"
time="2025-06-27 18:44:02" level=info msg="执行一次性同步: student_basic"
time="2025-06-27 18:44:02" level=info msg="创建表: CREATE TABLE `student_basic` (`college_name` VARCHAR(100) COMMENT '学院名称', `student_status_code` VARCHAR(10) COMMENT '学生当前状态码', `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间', `name_pinyin` VARCHAR(100) COMMENT '姓名拼音', `major_code` VARCHAR(20) COMMENT '专业代码', `class_code` VARCHAR(20) COMMENT '班级代码', `class_name` VARCHAR(100) COMMENT '班级名称', `email` VARCHAR(100) COMMENT '电子邮箱', `student_number` VARCHAR(20) PRIMARY KEY COMMENT '学号', `student_name` VARCHAR(50) NOT NULL COMMENT '学生姓名', `english_name` VARCHAR(100) COMMENT '英文姓名', `college_code` VARCHAR(20) COMMENT '学院代码', `major_name` VARCHAR(100) COMMENT '专业名称', `student_status` VARCHAR(20) COMMENT '学生当前状态', `student_id_number` VARCHAR(30) COMMENT '学籍号', `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间') ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
time="2025-06-27 18:44:02" level=info msg="表 student_basic 创建成功"
time="2025-06-27 18:44:02" level=info msg="开始同步API数据: student_basic"
time="2025-06-27 18:44:02" level=info msg="表 student_basic 已存在"
time="2025-06-27 18:44:02" level=info msg="开始获取API数据: student_basic"
time="2025-06-27 18:44:02" level=info msg="成功获取API数据: student_basic，共 13 条记录"
time="2025-06-27 18:44:02" level=info msg="数据转换完成，共处理 13 条记录"
time="2025-06-27 18:44:02" level=info msg="成功更新插入 13 条数据到表 student_basic"
time="2025-06-27 18:44:02" level=info msg="API数据同步完成: student_basic，耗时: 184.659875ms，记录数: 13"
time="2025-06-27 18:44:02" level=info msg="一次性同步完成: student_basic"
time="2025-06-27 18:54:05" level=info msg="日志输出到文件: data_sync.log"
time="2025-06-27 18:54:05" level=info msg="启动 api2mysql v1.0.0"
time="2025-06-27 18:54:05" level=info msg="使用配置文件: config-production.yaml"
time="2025-06-27 18:54:05" level=info msg="执行一次性同步: teacher_basic"
time="2025-06-27 18:54:05" level=info msg="创建表: CREATE TABLE `teacher_basic` (`updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间', `employee_number` VARCHAR(20) PRIMARY KEY COMMENT '员工工号', `employee_name` VARCHAR(50) NOT NULL COMMENT '员工姓名', `department_code` VARCHAR(20) COMMENT '部门编码', `position_level` VARCHAR(20) COMMENT '职位级别/是否主管', `employment_status` VARCHAR(20) COMMENT '岗位当前状态', `gender` VARCHAR(10) COMMENT '性别', `department_name` VARCHAR(100) COMMENT '部门名称', `job_category` VARCHAR(50) COMMENT '岗位类别', `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间') ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
time="2025-06-27 18:54:05" level=info msg="表 teacher_basic 创建成功"
time="2025-06-27 18:54:05" level=info msg="开始同步API数据: teacher_basic"
time="2025-06-27 18:54:05" level=info msg="表 teacher_basic 已存在"
time="2025-06-27 18:54:05" level=info msg="开始获取API数据: teacher_basic"
time="2025-06-27 18:54:05" level=info msg="成功获取API数据: teacher_basic，共 118 条记录"
time="2025-06-27 18:54:05" level=info msg="数据转换完成，共处理 118 条记录"
time="2025-06-27 18:54:05" level=error msg="数据同步到数据库失败 [teacher_basic]: 更新插入数据失败: Error 1048: Column 'employee_number' cannot be null"
time="2025-06-27 18:54:05" level=info msg="一次性同步完成: teacher_basic"
time="2025-06-27 18:59:29" level=info msg="日志输出到文件: data_sync.log"
time="2025-06-27 18:59:29" level=info msg="启动 api2mysql v1.0.0"
time="2025-06-27 18:59:29" level=info msg="使用配置文件: config-production.yaml"
time="2025-06-27 18:59:29" level=info msg="执行一次性同步: teacher_basic"
time="2025-06-27 18:59:29" level=info msg="表 teacher_basic 已存在"
time="2025-06-27 18:59:29" level=info msg="开始同步API数据: teacher_basic"
time="2025-06-27 18:59:29" level=info msg="表 teacher_basic 已存在"
time="2025-06-27 18:59:29" level=info msg="开始获取API数据: teacher_basic"
time="2025-06-27 18:59:29" level=info msg="成功获取API数据: teacher_basic，共 830 条记录"
time="2025-06-27 18:59:29" level=info msg="数据转换完成，共处理 830 条记录"
time="2025-06-27 18:59:29" level=info msg="成功更新插入 830 条数据到表 teacher_basic"
time="2025-06-27 18:59:29" level=info msg="API数据同步完成: teacher_basic，耗时: 478.231417ms，记录数: 830"
time="2025-06-27 18:59:29" level=info msg="一次性同步完成: teacher_basic"
time="2025-07-22 09:56:03" level=info msg="日志输出到文件: data_sync.log"
time="2025-07-22 09:56:03" level=info msg="启动 api2mysql v1.0.0"
time="2025-07-22 09:56:03" level=info msg="使用配置文件: config-production.yaml"
time="2025-07-22 09:56:03" level=info msg="执行一次性同步: teacher_basic"
time="2025-07-22 09:56:03" level=info msg="表 teacher_basic 已存在"
time="2025-07-22 09:56:03" level=info msg="开始同步API数据: teacher_basic"
time="2025-07-22 09:56:03" level=info msg="表 teacher_basic 已存在"
time="2025-07-22 09:56:03" level=info msg="开始获取API数据: teacher_basic"
time="2025-07-22 09:56:03" level=info msg="开始分页获取数据，每页 100 条记录"
time="2025-07-22 09:56:03" level=error msg="获取API数据失败 (尝试 1/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:56:03" level=warning msg="第 1 次重试获取API数据: teacher_basic"
time="2025-07-22 09:56:04" level=error msg="获取API数据失败 (尝试 2/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:56:04" level=warning msg="第 2 次重试获取API数据: teacher_basic"
time="2025-07-22 09:56:06" level=error msg="获取API数据失败 (尝试 3/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:56:06" level=warning msg="第 3 次重试获取API数据: teacher_basic"
time="2025-07-22 09:56:09" level=error msg="获取API数据失败 (尝试 4/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:56:09" level=warning msg="第 4 次重试获取API数据: teacher_basic"
time="2025-07-22 09:56:13" level=error msg="获取API数据失败 (尝试 5/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:56:13" level=warning msg="第 5 次重试获取API数据: teacher_basic"
time="2025-07-22 09:56:18" level=error msg="获取API数据失败 (尝试 6/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:56:18" level=error msg="获取API数据失败 [teacher_basic]: 获取第 1 页数据失败: 获取API数据失败，已重试 5 次: HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:56:18" level=info msg="一次性同步完成: teacher_basic"
time="2025-07-22 09:56:33" level=info msg="日志输出到文件: data_sync.log"
time="2025-07-22 09:56:33" level=info msg="启动 api2mysql v1.0.0"
time="2025-07-22 09:56:33" level=info msg="使用配置文件: config-production.yaml"
time="2025-07-22 09:56:33" level=info msg="执行一次性同步: student_basic"
time="2025-07-22 09:56:33" level=info msg="表 student_basic 已存在"
time="2025-07-22 09:56:33" level=info msg="开始同步API数据: student_basic"
time="2025-07-22 09:56:33" level=info msg="表 student_basic 已存在"
time="2025-07-22 09:56:33" level=info msg="开始获取API数据: student_basic"
time="2025-07-22 09:56:33" level=info msg="开始分页获取数据，每页 100 条记录"
time="2025-07-22 09:56:33" level=error msg="获取API数据失败 (尝试 1/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:56:33" level=warning msg="第 1 次重试获取API数据: student_basic"
time="2025-07-22 09:56:34" level=error msg="获取API数据失败 (尝试 2/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:56:34" level=warning msg="第 2 次重试获取API数据: student_basic"
time="2025-07-22 09:56:36" level=error msg="获取API数据失败 (尝试 3/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:56:36" level=warning msg="第 3 次重试获取API数据: student_basic"
time="2025-07-22 09:56:39" level=error msg="获取API数据失败 (尝试 4/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:56:39" level=warning msg="第 4 次重试获取API数据: student_basic"
time="2025-07-22 09:56:43" level=error msg="获取API数据失败 (尝试 5/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:56:43" level=warning msg="第 5 次重试获取API数据: student_basic"
time="2025-07-22 09:56:48" level=error msg="获取API数据失败 (尝试 6/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:56:48" level=error msg="获取API数据失败 [student_basic]: 获取第 1 页数据失败: 获取API数据失败，已重试 5 次: HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:56:48" level=info msg="一次性同步完成: student_basic"
time="2025-07-22 09:57:25" level=info msg="日志输出到文件: data_sync.log"
time="2025-07-22 09:57:25" level=info msg="启动 api2mysql v1.0.0"
time="2025-07-22 09:57:25" level=info msg="使用配置文件: config-production.yaml"
time="2025-07-22 09:57:25" level=info msg="执行一次性同步: course_schedule"
time="2025-07-22 09:57:25" level=info msg="创建表: CREATE TABLE `course_schedule` (`qr_code_name` VARCHAR(100) COMMENT '二维码名称', `schedule_id` VARCHAR(50) PRIMARY KEY COMMENT '排课ID', `course_weeks` VARCHAR(100) COMMENT '开课周次', `weekday` VARCHAR(5) COMMENT '星期', `operator` VARCHAR(50) COMMENT '操作人', `update_time` VARCHAR(50) COMMENT '更新时间', `course_weeks_detail` VARCHAR(200) COMMENT '开课周次明细', `recording_reason` VARCHAR(100) COMMENT '录播原因', `link_name` VARCHAR(100) COMMENT '链接名称', `semester_id` VARCHAR(20) COMMENT '学年学期ID', `course_time` VARCHAR(20) COMMENT '课程时间', `schedule_class` VARCHAR(100) COMMENT '排课班级', `lab_reason_id` VARCHAR(50) COMMENT '实验室原因ID', `course_location_code` VARCHAR(10) COMMENT '开课地点代码', `course_time_code` VARCHAR(20) COMMENT '课程时间编号', `schedule_category` VARCHAR(50) COMMENT '排课类别', `teacher_id` VARCHAR(50) COMMENT '教师ID', `operation_time` VARCHAR(50) COMMENT '操作时间', `semester` VARCHAR(20) COMMENT '学年学期', `course_time_detail` VARCHAR(100) COMMENT '课程时间明细', `course_basic_id` VARCHAR(50) COMMENT '课表基础描述ID', `create_time` VARCHAR(50) COMMENT '创建时间', `actual_end_time` TIME COMMENT '实际结束时间', `course_location_type` VARCHAR(20) COMMENT '开课地点类别', `time_flag` VARCHAR(20) COMMENT '时间标志', `end_time` TIME COMMENT '结束时间', `substitute_ip` VARCHAR(50) COMMENT '代课IP', `is_special_lab` VARCHAR(5) COMMENT '是否特殊实验室', `substitute_teacher_id` VARCHAR(50) COMMENT '代课教师ID', `link_type` VARCHAR(20) COMMENT '链接类型', `actual_start_time` TIME COMMENT '实际开始时间', `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间', `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间', `start_time` TIME COMMENT '开始时间', `substitute_flag` VARCHAR(10) COMMENT '代课标志', `old_teacher_id` VARCHAR(50) COMMENT '原教师ID', `is_record_deleted` VARCHAR(5) COMMENT '记录是否删除', `qr_code_address` VARCHAR(200) COMMENT '二维码地址') ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
time="2025-07-22 09:57:25" level=info msg="表 course_schedule 创建成功"
time="2025-07-22 09:57:25" level=info msg="开始同步API数据: course_schedule"
time="2025-07-22 09:57:25" level=info msg="表 course_schedule 已存在"
time="2025-07-22 09:57:25" level=info msg="开始获取API数据: course_schedule"
time="2025-07-22 09:57:25" level=info msg="开始分页获取数据，每页 100 条记录"
time="2025-07-22 09:57:25" level=error msg="获取API数据失败 (尝试 1/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:57:25" level=warning msg="第 1 次重试获取API数据: course_schedule"
time="2025-07-22 09:57:26" level=error msg="获取API数据失败 (尝试 2/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:57:26" level=warning msg="第 2 次重试获取API数据: course_schedule"
time="2025-07-22 09:57:29" level=error msg="获取API数据失败 (尝试 3/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:57:29" level=warning msg="第 3 次重试获取API数据: course_schedule"
time="2025-07-22 09:57:32" level=error msg="获取API数据失败 (尝试 4/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:57:32" level=warning msg="第 4 次重试获取API数据: course_schedule"
time="2025-07-22 09:57:36" level=error msg="获取API数据失败 (尝试 5/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:57:36" level=warning msg="第 5 次重试获取API数据: course_schedule"
time="2025-07-22 09:57:41" level=error msg="获取API数据失败 (尝试 6/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:57:41" level=error msg="获取API数据失败 [course_schedule]: 获取第 1 页数据失败: 获取API数据失败，已重试 5 次: HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:57:41" level=info msg="一次性同步完成: course_schedule"
time="2025-07-22 09:57:55" level=info msg="日志输出到文件: data_sync.log"
time="2025-07-22 09:57:55" level=info msg="启动 api2mysql v1.0.0"
time="2025-07-22 09:57:55" level=info msg="使用配置文件: config-production.yaml"
time="2025-07-22 09:57:55" level=info msg="执行一次性同步: research_project"
time="2025-07-22 09:57:55" level=info msg="创建表: CREATE TABLE `research_project` (`project_source_name` VARCHAR(100) COMMENT '项目来源名称', `participation_form_code` VARCHAR(20) COMMENT '参与方式码', `social_science_code` VARCHAR(20) COMMENT '社会科学码', `participants_count` INT COMMENT '参与人数', `project_source_unit` VARCHAR(200) COMMENT '项目来源单位', `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间', `leader_dept_code` VARCHAR(50) COMMENT '项目负责人所在单位码', `secondary_discipline_code` VARCHAR(20) COMMENT '二级学科码', `entrusting_country_code` VARCHAR(20) COMMENT '委托单位国家码', `social_science_name` VARCHAR(100) COMMENT '社会科学名称', `activity_type_name` VARCHAR(100) COMMENT '活动类型名称', `project_type_code` VARCHAR(10) COMMENT '项目分类码', `project_type_name` VARCHAR(50) COMMENT '项目分类名称', `company_platform_name` VARCHAR(200) COMMENT '公司阶段平台名称', `research_discipline_code` VARCHAR(20) COMMENT '研究学科码', `operation_type` VARCHAR(50) COMMENT '操作类型', `unit_level_name` VARCHAR(100) COMMENT '单位级别名称', `participating_teachers` INT COMMENT '参与教师人数', `planned_completion_name` VARCHAR(100) COMMENT '计划完成情况名称', `project_number` VARCHAR(50) COMMENT '项目编号', `project_status` VARCHAR(50) COMMENT '项目状态', `leader_phone` VARCHAR(20) COMMENT '负责人电话', `research_type_name` VARCHAR(100) COMMENT '研究类别名称', `project_secondary_category_name` VARCHAR(100) COMMENT '项目二级类别名称', `cooperation_country_name` VARCHAR(100) COMMENT '合作国家地区名称', `submission_date` DATE COMMENT '申报日期', `entrusting_unit` VARCHAR(200) COMMENT '项目委托单位', `service_field_name` VARCHAR(100) COMMENT '服务领域名称', `unit_name` VARCHAR(200) COMMENT '单位名称', `execution_number` VARCHAR(50) COMMENT '执行编号', `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间', `processing_date` DATE COMMENT '处理日期', `project_name` VARCHAR(200) NOT NULL COMMENT '项目名称', `project_approval_number` VARCHAR(50) COMMENT '项目批准号', `equipment_funding` DECIMAL(15,2) COMMENT '设备经费', `participation_form_name` VARCHAR(100) COMMENT '参与方式名称', `social_economic_code` VARCHAR(20) COMMENT '社会经济效益码', `contact_phone` VARCHAR(20) COMMENT '项目联系人电话', `contract_funding` DECIMAL(15,2) COMMENT '合同经费', `contact_job_number` VARCHAR(50) COMMENT '项目联系人职工号', `company_unit_code` VARCHAR(50) COMMENT '公司单位码', `contract_number` VARCHAR(50) COMMENT '合同编号', `data_source` VARCHAR(100) COMMENT '数据来源', `project_leader_name` VARCHAR(100) COMMENT '项目负责人名称', `research_type_code` VARCHAR(20) COMMENT '研究类别码', `cooperation_country_code` VARCHAR(20) COMMENT '合作国家地区码', `project_source_stat_code` VARCHAR(20) COMMENT '项目来源统计码', `discipline_category_name` VARCHAR(100) COMMENT '学科门类科技名称', `project_source_code` VARCHAR(20) COMMENT '项目来源码', `reserved_field1` VARCHAR(200) COMMENT '备用字段1', `reserved_field2` VARCHAR(200) COMMENT '备用字段2', `end_date` DATE COMMENT '结束日期', `entrusting_country_name` VARCHAR(100) COMMENT '委托单位国家名称', `project_level_name` VARCHAR(100) COMMENT '项目级别名称', `planned_end_date` DATE COMMENT '计划完成日期', `planned_completion_code` VARCHAR(20) COMMENT '计划完成情况码', `project_source_stat_name` VARCHAR(100) COMMENT '项目来源统计名称', `social_economic_target_name` VARCHAR(100) COMMENT '社会经济目标名称', `end_time` VARCHAR(50) COMMENT '结束时间', `leader_dept_name` VARCHAR(200) COMMENT '项目负责人所在单位名称', `company_unit_name` VARCHAR(200) COMMENT '公司单位名称', `execution_status_name` VARCHAR(100) COMMENT '项目执行状态名称', `cooperation_form_code` VARCHAR(20) COMMENT '合作形式码', `discipline_source_name` VARCHAR(100) COMMENT '学科来源名称', `research_discipline_name` VARCHAR(100) COMMENT '研究学科名称', `unit_number` VARCHAR(50) COMMENT '单位号', `competent_department` VARCHAR(100) COMMENT '主管部门', `project_sub_category_name` VARCHAR(100) COMMENT '项目类别名称', `project_description` TEXT COMMENT '项目简介', `contact_email` VARCHAR(100) COMMENT '项目联系人邮箱', `technology_source` VARCHAR(200) COMMENT '所属技术领域', `project_leader_code` VARCHAR(50) COMMENT '项目负责人号', `subject_code` VARCHAR(20) COMMENT '门类码', `service_field_code` VARCHAR(20) COMMENT '服务领域码', `secondary_discipline_name` VARCHAR(100) COMMENT '二级学科名称', `cooperation_form_name` VARCHAR(100) COMMENT '合作形式名称', `activity_type_code` VARCHAR(20) COMMENT '活动类型码', `project_secondary_category_code` VARCHAR(20) COMMENT '项目二级类别码', `execution_status_code` VARCHAR(20) COMMENT '项目执行状态码', `start_topic_date` DATE COMMENT '开题日期', `social_economic_target` VARCHAR(200) COMMENT '社会经济目标', `research_category_name` VARCHAR(50) COMMENT '科研大类名称', `project_sub_category_code` VARCHAR(20) COMMENT '项目类别码', `project_summary` TEXT COMMENT '项目摘要', `planned_start_date` DATE COMMENT '计划开始日期', `expected_research_results` TEXT COMMENT '预期研究成果形式', `contact_name` VARCHAR(100) COMMENT '项目联系人姓名', `asset_funding` DECIMAL(15,2) COMMENT '资产经费', `project_stage_name` VARCHAR(100) COMMENT '项目阶段形式名称', `remarks` TEXT COMMENT '备注', `project_id` VARCHAR(50) PRIMARY KEY COMMENT '项目ID', `approval_date` DATE COMMENT '立项日期', `research_team` VARCHAR(200) COMMENT '所属组课题', `etl_flag` VARCHAR(20) COMMENT 'ETL标志', `leader_email` VARCHAR(100) COMMENT '负责人邮箱', `platform_funding` DECIMAL(15,2) COMMENT '平台经费', `entrusting_region_code` VARCHAR(20) COMMENT '委托单位所在地区码', `subject_name` VARCHAR(100) COMMENT '门类名称', `school_ranking` VARCHAR(20) COMMENT '学校排名', `company_platform_code` VARCHAR(50) COMMENT '公司阶段平台码', `social_economic_name` VARCHAR(100) COMMENT '社会经济效益名称', `project_stage_code` VARCHAR(20) COMMENT '项目阶段形式码', `participation_density` VARCHAR(50) COMMENT '参与密度', `start_date` DATE COMMENT '开始日期', `project_level_code` VARCHAR(20) COMMENT '项目级别码', `total_funding` DECIMAL(15,2) COMMENT '总投资', `entrusting_region_name` VARCHAR(100) COMMENT '委托单位所在地区名称', `organization_form_code` VARCHAR(20) COMMENT '组织形式码', `organization_form_name` VARCHAR(100) COMMENT '组织形式名称', `social_economic_target_code` VARCHAR(20) COMMENT '社会经济目标码', `discipline_category_code` VARCHAR(20) COMMENT '学科门类科技码', `submission_project_number` VARCHAR(50) COMMENT '申报项目号', `coordination_unit_number` VARCHAR(50) COMMENT '协调单位号', `software_funding` DECIMAL(15,2) COMMENT '软件经费', `unit_level_code` VARCHAR(20) COMMENT '单位级别码', `participating_students` INT COMMENT '参与学生人数', `research_category_code` VARCHAR(10) COMMENT '科研大类码', `discipline_source_code` VARCHAR(20) COMMENT '学科来源码', `start_time` VARCHAR(50) COMMENT '开始时间') ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
time="2025-07-22 09:57:55" level=info msg="表 research_project 创建成功"
time="2025-07-22 09:57:55" level=info msg="开始同步API数据: research_project"
time="2025-07-22 09:57:55" level=info msg="表 research_project 已存在"
time="2025-07-22 09:57:55" level=info msg="开始获取API数据: research_project"
time="2025-07-22 09:57:55" level=info msg="开始分页获取数据，每页 100 条记录"
time="2025-07-22 09:57:55" level=error msg="获取API数据失败 (尝试 1/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:57:55" level=warning msg="第 1 次重试获取API数据: research_project"
time="2025-07-22 09:57:56" level=error msg="获取API数据失败 (尝试 2/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:57:56" level=warning msg="第 2 次重试获取API数据: research_project"
time="2025-07-22 09:57:58" level=error msg="获取API数据失败 (尝试 3/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:57:58" level=warning msg="第 3 次重试获取API数据: research_project"
time="2025-07-22 09:58:01" level=error msg="获取API数据失败 (尝试 4/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:58:01" level=warning msg="第 4 次重试获取API数据: research_project"
time="2025-07-22 09:58:05" level=error msg="获取API数据失败 (尝试 5/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:58:05" level=warning msg="第 5 次重试获取API数据: research_project"
time="2025-07-22 09:58:10" level=error msg="获取API数据失败 (尝试 6/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:58:10" level=error msg="获取API数据失败 [research_project]: 获取第 1 页数据失败: 获取API数据失败，已重试 5 次: HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:58:10" level=info msg="一次性同步完成: research_project"
time="2025-07-22 09:58:16" level=info msg="日志输出到文件: data_sync.log"
time="2025-07-22 09:58:16" level=info msg="启动 api2mysql v1.0.0"
time="2025-07-22 09:58:16" level=info msg="使用配置文件: config-production.yaml"
time="2025-07-22 09:58:16" level=info msg="执行一次性同步: student_status_change"
time="2025-07-22 09:58:16" level=info msg="创建表: CREATE TABLE `student_status_change` (`counselor_reviewer` VARCHAR(50) COMMENT '辅导员审核人', `registrar_review_time` VARCHAR(50) COMMENT '学籍管理员审核时间', `academic_office_file_name` VARCHAR(200) COMMENT '教务处审核附件名', `original_graduate_major` VARCHAR(50) COMMENT '原毕业专业', `change_end_time` VARCHAR(50) COMMENT '异动结束时间', `study_end_date` DATE COMMENT '学习结束日期', `dean_review` VARCHAR(20) COMMENT '学院领导审核', `registrar_review` VARCHAR(20) COMMENT '学籍管理员审核', `transfer_file_path` VARCHAR(500) COMMENT '转入附件路径', `expected_graduate_time` VARCHAR(50) COMMENT '预计毕业时间', `change_reason` VARCHAR(100) COMMENT '异动原因', `change_method` VARCHAR(50) COMMENT '异动方式', `academic_office_review` VARCHAR(20) COMMENT '教务处审核', `original_study_years` VARCHAR(10) COMMENT '原学制', `new_major_code` VARCHAR(50) COMMENT '新专业码', `new_graduate_major` VARCHAR(50) COMMENT '新毕业专业', `registrar_reviewer` VARCHAR(50) COMMENT '学籍管理员审核人', `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间', `counselor_review_time` VARCHAR(50) COMMENT '辅导员审核时间', `is_school_internal_change` VARCHAR(5) COMMENT '是否校内异动', `handle_start_time` VARCHAR(50) COMMENT '办理入学资格开始时间', `change_target_code` VARCHAR(50) COMMENT '异动去向学校码', `new_status_code` VARCHAR(10) COMMENT '新学籍状态码', `new_current_status` VARCHAR(30) COMMENT '新学生当前状态', `semester` VARCHAR(20) COMMENT '生效学年学期', `record_location` VARCHAR(100) COMMENT '记录地点', `handle_end_time` VARCHAR(50) COMMENT '办理入学资格结束时间', `change_source_code` VARCHAR(50) COMMENT '异动来源学校码', `original_college_name` VARCHAR(100) COMMENT '原院系名称', `return_date` DATE COMMENT '复学日期', `original_exchange_level` VARCHAR(50) COMMENT '原国际交流级', `college_review` VARCHAR(20) COMMENT '所在院系审核', `record_id` VARCHAR(50) PRIMARY KEY COMMENT '记录ID', `change_type` VARCHAR(50) COMMENT '异动类别', `approval_date` DATE COMMENT '审批日期', `handle_status` VARCHAR(20) COMMENT '办理状态', `academic_office_reviewer` VARCHAR(50) COMMENT '教务处审核人', `change_description` TEXT COMMENT '异动说明', `new_class_name` VARCHAR(100) COMMENT '新班级', `new_exchange_level` VARCHAR(50) COMMENT '新国际交流级', `record_start_time` VARCHAR(50) COMMENT '记录开始时间', `college_file_name` VARCHAR(200) COMMENT '所在院系附件名', `new_status` VARCHAR(30) COMMENT '新学籍状态', `study_start_date` DATE COMMENT '学习开始日期', `student_change_file_name` VARCHAR(200) COMMENT '学生异动附件名', `approval_document` VARCHAR(100) COMMENT '审批文号', `original_major_code` VARCHAR(50) COMMENT '原专业码', `original_grade` VARCHAR(10) COMMENT '原年级', `original_status_code` VARCHAR(10) COMMENT '原学籍状态码', `new_college_name` VARCHAR(100) COMMENT '新院系名称', `dean_reviewer` VARCHAR(50) COMMENT '学院领导审核人', `dean_review_time` VARCHAR(50) COMMENT '学院领导审核时间', `college_review_time` VARCHAR(50) COMMENT '所在院系审核时间', `target_college_reviewer` VARCHAR(50) COMMENT '转入院系审核人', `record_end_time` VARCHAR(50) COMMENT '记录结束时间', `target_college_review_time` VARCHAR(50) COMMENT '转入院系审核时间', `transfer_requirement` VARCHAR(200) COMMENT '转出/转入要求', `original_class_name` VARCHAR(100) COMMENT '原班级', `new_grade` VARCHAR(10) COMMENT '新年级', `approval_status` VARCHAR(20) COMMENT '审批状态', `old_school_status_code` VARCHAR(10) COMMENT '原在校状态码', `old_school_status` VARCHAR(30) COMMENT '原在校状态', `change_type_code` VARCHAR(10) COMMENT '异动类别码', `change_reason_code` VARCHAR(10) COMMENT '异动原因码', `target_college_review` VARCHAR(20) COMMENT '转入院系审核', `change_date` DATE COMMENT '异动日期', `original_major_name` VARCHAR(100) COMMENT '原专业名称', `original_status` VARCHAR(30) COMMENT '原学籍状态', `new_major_name` VARCHAR(100) COMMENT '新专业名称', `class_arrangement` VARCHAR(100) COMMENT '班级安排', `academic_office_review_time` VARCHAR(50) COMMENT '教务处审核时间', `original_student_number` VARCHAR(20) COMMENT '原学号', `original_college_code` VARCHAR(20) COMMENT '原院系号', `new_study_years` VARCHAR(10) COMMENT '新学制', `new_current_status_code` VARCHAR(10) COMMENT '新学生当前状态码', `school_status_code` VARCHAR(10) COMMENT '在校状态码', `application_reason` VARCHAR(200) COMMENT '申请理由', `new_graduate_date` DATE COMMENT '新毕业日期', `student_change_file_path` VARCHAR(500) COMMENT '学生异动附件路径', `transfer_grade_year` VARCHAR(20) COMMENT '转出/转入年级', `external_review_status` VARCHAR(20) COMMENT '外部审核状态', `new_class_code` VARCHAR(50) COMMENT '新班号', `original_graduate_date` DATE COMMENT '原毕业日期', `process_status` VARCHAR(20) COMMENT '处理状态', `new_process_status` VARCHAR(20) COMMENT '新处理状态', `transfer_file_name` VARCHAR(200) COMMENT '转入附件名', `college_reviewer` VARCHAR(50) COMMENT '所在院系审核人', `academic_office_file_path` VARCHAR(500) COMMENT '教务处审核附件路径', `college_file_path` VARCHAR(500) COMMENT '所在院系附件路径', `new_student_number` VARCHAR(20) COMMENT '新学号', `student_name` VARCHAR(50) NOT NULL COMMENT '学生姓名', `original_class_code` VARCHAR(50) COMMENT '原班号', `original_current_status_code` VARCHAR(10) COMMENT '原学生当前状态码', `original_current_status` VARCHAR(30) COMMENT '原学生当前状态', `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间', `new_college_code` VARCHAR(20) COMMENT '新院系号', `school_status` VARCHAR(30) COMMENT '在校状态', `change_start_time` VARCHAR(50) COMMENT '异动开始时间', `counselor_review` VARCHAR(20) COMMENT '辅导员审核', `is_batch_process` VARCHAR(5) COMMENT '是否批量') ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
time="2025-07-22 09:58:16" level=info msg="表 student_status_change 创建成功"
time="2025-07-22 09:58:16" level=info msg="开始同步API数据: student_status_change"
time="2025-07-22 09:58:16" level=info msg="表 student_status_change 已存在"
time="2025-07-22 09:58:16" level=info msg="开始获取API数据: student_status_change"
time="2025-07-22 09:58:16" level=info msg="开始分页获取数据，每页 100 条记录"
time="2025-07-22 09:58:17" level=error msg="获取API数据失败 (尝试 1/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:58:17" level=warning msg="第 1 次重试获取API数据: student_status_change"
time="2025-07-22 09:58:18" level=error msg="获取API数据失败 (尝试 2/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:58:18" level=warning msg="第 2 次重试获取API数据: student_status_change"
time="2025-07-22 09:58:20" level=error msg="获取API数据失败 (尝试 3/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:58:20" level=warning msg="第 3 次重试获取API数据: student_status_change"
time="2025-07-22 09:58:23" level=error msg="获取API数据失败 (尝试 4/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:58:23" level=warning msg="第 4 次重试获取API数据: student_status_change"
time="2025-07-22 09:58:27" level=error msg="获取API数据失败 (尝试 5/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:58:27" level=warning msg="第 5 次重试获取API数据: student_status_change"
time="2025-07-22 09:58:32" level=error msg="获取API数据失败 (尝试 6/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:58:32" level=error msg="获取API数据失败 [student_status_change]: 获取第 1 页数据失败: 获取API数据失败，已重试 5 次: HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:58:32" level=info msg="一次性同步完成: student_status_change"
time="2025-07-22 09:58:40" level=info msg="日志输出到文件: data_sync.log"
time="2025-07-22 09:58:40" level=info msg="启动 api2mysql v1.0.0"
time="2025-07-22 09:58:40" level=info msg="使用配置文件: config-production.yaml"
time="2025-07-22 09:58:40" level=info msg="执行一次性同步: student_registration"
time="2025-07-22 09:58:40" level=info msg="创建表: CREATE TABLE `student_registration` (`research_direction` VARCHAR(200) COMMENT '研究方向', `supervisor_name` VARCHAR(100) COMMENT '导师姓名', `degree_type_code` VARCHAR(20) COMMENT '获得学历方式码', `student_type` VARCHAR(50) COMMENT '学生类型', `is_minor` VARCHAR(5) COMMENT '是否辅修', `is_university_training` VARCHAR(5) COMMENT '是否大学培养', `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间', `student_category_code` VARCHAR(20) COMMENT '学生类别码', `special_status_code` VARCHAR(20) COMMENT '学生身份特征方式码', `admission_subject` VARCHAR(100) COMMENT '招生学科', `registration_status_code` VARCHAR(10) COMMENT '学籍状态码', `expected_graduation_time` VARCHAR(50) COMMENT '预计毕业时间', `is_mobile` VARCHAR(5) COMMENT '是否异动', `degree` VARCHAR(50) COMMENT '学位', `degree_type` VARCHAR(100) COMMENT '获得学历方式', `is_rural_student` VARCHAR(5) COMMENT '是否农村学生', `minor_graduation_level` VARCHAR(50) COMMENT '辅修毕业级别', `school_card_id` VARCHAR(50) COMMENT '校园卡ID', `specialization` VARCHAR(100) COMMENT '专项', `bachelor_language_certificate` VARCHAR(100) COMMENT '本科语言证书号', `study_form_code` VARCHAR(20) COMMENT '学习形式码', `enrollment_method_code` VARCHAR(20) COMMENT '录取方式码', `training_method` VARCHAR(100) COMMENT '培养方式', `enrollment_method` VARCHAR(100) COMMENT '录取方式', `is_teacher_training` VARCHAR(5) COMMENT '是否师范生', `degree_certificate_number` VARCHAR(100) COMMENT '学位证书号', `specialization_certificate` VARCHAR(100) COMMENT '专项证', `completion_certificate_number` VARCHAR(100) COMMENT '结业证书号', `degree_award_time2` VARCHAR(50) COMMENT '授予学位时间', `bachelor_experience` VARCHAR(100) COMMENT '本科毕业经历', `obtained_degree` VARCHAR(50) COMMENT '得学位', `minor_degree_award_time` VARCHAR(50) COMMENT '授予辅修学士学位时间', `special_status` VARCHAR(100) COMMENT '学生身份特征方式', `graduation_review_result` VARCHAR(50) COMMENT '毕业审核结果', `college_code` VARCHAR(20) COMMENT '院系代码', `supervisor_number` VARCHAR(50) COMMENT '导师号', `study_form` VARCHAR(100) COMMENT '学习形式', `exam_number` VARCHAR(50) COMMENT '考生号', `minor_class_id` VARCHAR(50) COMMENT '辅修班级ID', `class_code` VARCHAR(20) COMMENT '班级代码', `current_grade` VARCHAR(10) COMMENT '当前所在级', `major_code` VARCHAR(20) COMMENT '专业代码', `enrollment_category` VARCHAR(50) COMMENT '招生类别', `second_degree_award_time` VARCHAR(50) COMMENT '授予第二学士学位时间', `discipline_category` VARCHAR(100) COMMENT '学科门类', `minor_major_id` VARCHAR(50) COMMENT '辅修专业ID', `discipline_category_code` VARCHAR(20) COMMENT '学科门类码', `graduation_major` VARCHAR(100) COMMENT '毕业专业', `is_scholarship` VARCHAR(5) COMMENT '是否有奖学金', `bachelor_education_certificate` VARCHAR(100) COMMENT '本科教育证书号', `bachelor_degree_award_time` VARCHAR(50) COMMENT '学士学位授予时间', `bachelor_language_time` VARCHAR(50) COMMENT '本科语言时间', `student_training_level_code` VARCHAR(20) COMMENT '学生培养层次码', `student_category` VARCHAR(50) COMMENT '学生类别', `execution_plan_id` VARCHAR(50) COMMENT '执行计划ID', `bachelor_experience_code` VARCHAR(20) COMMENT '本科毕业经历码', `graduation_type_code` VARCHAR(20) COMMENT '毕业类型码', `school_status_code` VARCHAR(10) COMMENT '在校状态码', `is_expected_graduate` VARCHAR(5) COMMENT '是否预计毕业生', `master_graduation_time` VARCHAR(50) COMMENT '研究生毕业时间', `bachelor_degree_name` VARCHAR(100) COMMENT '学士学位名称', `study_duration` VARCHAR(10) COMMENT '学生学制', `training_level_code` VARCHAR(20) COMMENT '培养层次码', `international_exchange` VARCHAR(100) COMMENT '国际交流', `minor_certificate_time` VARCHAR(50) COMMENT '辅修证书获得时间', `is_high_level_talent` VARCHAR(5) COMMENT '是否高层次人才', `college_other_info` VARCHAR(200) COMMENT '学院其他信息', `college_first_choice_status` VARCHAR(50) COMMENT '院校第一志愿状态', `degree_review_result` VARCHAR(50) COMMENT '学位审核结果', `graduation_type` VARCHAR(50) COMMENT '毕业类型', `second_degree_certificate_number` VARCHAR(100) COMMENT '第二学士学位证书号', `degree_award_time` VARCHAR(50) COMMENT '学位授予时间', `graduation_certificate_number` VARCHAR(100) COMMENT '毕业证书号', `is_master` VARCHAR(5) COMMENT '是否硕士', `bachelor_certificate_number` VARCHAR(100) COMMENT '本科毕业证书号', `degree_certificate_number2` VARCHAR(100) COMMENT '学位证书号2', `registration_category_code` VARCHAR(20) COMMENT '学籍类别码', `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间', `college_name` VARCHAR(100) COMMENT '院系名称', `class_name` VARCHAR(100) COMMENT '班级名称', `medical_account` VARCHAR(50) COMMENT '医疗账号', `graduation_certificate_time` VARCHAR(50) COMMENT '获得毕业证书时间', `minor_degree_certificate_number` VARCHAR(100) COMMENT '辅修学士学位证书号', `major_name` VARCHAR(100) COMMENT '专业名称', `enrollment_test_language` VARCHAR(50) COMMENT '入学考试语种', `bachelor_graduation_time` VARCHAR(50) COMMENT '本科毕业时间', `student_name` VARCHAR(50) NOT NULL COMMENT '学生姓名', `current_status` VARCHAR(30) COMMENT '学生当前状态', `school_status` VARCHAR(30) COMMENT '在校状态', `student_file_id` VARCHAR(50) COMMENT '学生档案ID', `minor_certificate_number` VARCHAR(100) COMMENT '辅修证书号', `student_number` VARCHAR(20) NOT NULL COMMENT '学号', `record_id` VARCHAR(50) PRIMARY KEY COMMENT '记录ID', `is_category` VARCHAR(5) COMMENT '是否类别', `registration_status` VARCHAR(30) COMMENT '学籍状态', `enrollment_year` VARCHAR(20) COMMENT '入学年月', `training_level` VARCHAR(100) COMMENT '培养层次', `transfer_grade` VARCHAR(20) COMMENT '转出/转入年级', `bachelor_certificate_time` VARCHAR(50) COMMENT '本科毕业证书时间', `university_training_unit` VARCHAR(200) COMMENT '大学培养单位', `is_credit_system` VARCHAR(5) COMMENT '是否学分制', `current_status_code` VARCHAR(10) COMMENT '学生当前状态码', `minor_category_id` VARCHAR(50) COMMENT '辅修分类别ID', `graduation_time` VARCHAR(50) COMMENT '毕业时间', `bachelor_education_time` VARCHAR(50) COMMENT '本科教育时间', `training_method_code` VARCHAR(20) COMMENT '培养方式码') ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
time="2025-07-22 09:58:40" level=info msg="表 student_registration 创建成功"
time="2025-07-22 09:58:40" level=info msg="开始同步API数据: student_registration"
time="2025-07-22 09:58:40" level=info msg="表 student_registration 已存在"
time="2025-07-22 09:58:40" level=info msg="开始获取API数据: student_registration"
time="2025-07-22 09:58:40" level=info msg="开始分页获取数据，每页 100 条记录"
time="2025-07-22 09:58:41" level=error msg="获取API数据失败 (尝试 1/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:58:41" level=warning msg="第 1 次重试获取API数据: student_registration"
time="2025-07-22 09:58:42" level=error msg="获取API数据失败 (尝试 2/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:58:42" level=warning msg="第 2 次重试获取API数据: student_registration"
time="2025-07-22 09:58:44" level=error msg="获取API数据失败 (尝试 3/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:58:44" level=warning msg="第 3 次重试获取API数据: student_registration"
time="2025-07-22 09:58:47" level=error msg="获取API数据失败 (尝试 4/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:58:47" level=warning msg="第 4 次重试获取API数据: student_registration"
time="2025-07-22 09:58:51" level=error msg="获取API数据失败 (尝试 5/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:58:51" level=warning msg="第 5 次重试获取API数据: student_registration"
time="2025-07-22 09:58:56" level=error msg="获取API数据失败 (尝试 6/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:58:56" level=error msg="获取API数据失败 [student_registration]: 获取第 1 页数据失败: 获取API数据失败，已重试 5 次: HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 09:58:56" level=info msg="一次性同步完成: student_registration"
time="2025-07-22 10:03:10" level=info msg="日志输出到文件: data_sync.log"
time="2025-07-22 10:03:10" level=info msg="启动 api2mysql v1.0.0"
time="2025-07-22 10:03:10" level=info msg="使用配置文件: config-production.yaml"
time="2025-07-22 10:03:10" level=info msg="执行一次性同步: student_registration"
time="2025-07-22 10:03:10" level=info msg="表 student_registration 已存在"
time="2025-07-22 10:03:10" level=info msg="开始同步API数据: student_registration"
time="2025-07-22 10:03:10" level=info msg="表 student_registration 已存在"
time="2025-07-22 10:03:10" level=info msg="开始获取API数据: student_registration"
time="2025-07-22 10:03:10" level=info msg="成功获取API数据: student_registration，共 9 条记录"
time="2025-07-22 10:03:10" level=info msg="数据转换完成，共处理 9 条记录"
time="2025-07-22 10:03:10" level=info msg="成功更新插入 9 条数据到表 student_registration"
time="2025-07-22 10:03:10" level=info msg="API数据同步完成: student_registration，耗时: 325.608917ms，记录数: 9"
time="2025-07-22 10:03:10" level=info msg="一次性同步完成: student_registration"
time="2025-07-22 10:12:05" level=info msg="日志输出到文件: data_sync.log"
time="2025-07-22 10:12:05" level=info msg="启动 api2mysql v1.0.0"
time="2025-07-22 10:12:05" level=info msg="使用配置文件: config-production.yaml"
time="2025-07-22 10:12:05" level=info msg="执行一次性同步: course_schedule"
time="2025-07-22 10:12:05" level=info msg="表 course_schedule 已存在"
time="2025-07-22 10:12:05" level=info msg="开始同步API数据: course_schedule"
time="2025-07-22 10:12:05" level=info msg="表 course_schedule 已存在"
time="2025-07-22 10:12:05" level=info msg="开始获取API数据: course_schedule"
time="2025-07-22 10:12:06" level=error msg="获取API数据失败 (尝试 1/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 10:12:06" level=warning msg="第 1 次重试获取API数据: course_schedule"
time="2025-07-22 10:12:07" level=error msg="获取API数据失败 (尝试 2/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 10:12:07" level=warning msg="第 2 次重试获取API数据: course_schedule"
time="2025-07-22 10:12:09" level=error msg="获取API数据失败 (尝试 3/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 10:12:09" level=warning msg="第 3 次重试获取API数据: course_schedule"
time="2025-07-22 10:12:12" level=error msg="获取API数据失败 (尝试 4/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 10:12:12" level=warning msg="第 4 次重试获取API数据: course_schedule"
time="2025-07-22 10:12:16" level=error msg="获取API数据失败 (尝试 5/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 10:12:16" level=warning msg="第 5 次重试获取API数据: course_schedule"
time="2025-07-22 10:12:21" level=error msg="获取API数据失败 (尝试 6/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 10:12:21" level=error msg="获取API数据失败 [course_schedule]: 获取API数据失败，已重试 5 次: HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 10:12:21" level=info msg="一次性同步完成: course_schedule"
time="2025-07-22 10:19:28" level=info msg="日志输出到文件: data_sync.log"
time="2025-07-22 10:19:28" level=info msg="启动 api2mysql v1.0.0"
time="2025-07-22 10:19:28" level=info msg="使用配置文件: config-production.yaml"
time="2025-07-22 10:19:28" level=info msg="执行一次性同步: research_project"
time="2025-07-22 10:19:28" level=info msg="表 research_project 已存在"
time="2025-07-22 10:19:28" level=info msg="开始同步API数据: research_project"
time="2025-07-22 10:19:28" level=info msg="表 research_project 已存在"
time="2025-07-22 10:19:28" level=info msg="开始获取API数据: research_project"
time="2025-07-22 10:19:28" level=info msg="开始分页获取数据，每页 100 条记录"
time="2025-07-22 10:19:28" level=error msg="获取API数据失败 (尝试 1/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 10:19:28" level=warning msg="第 1 次重试获取API数据: research_project"
time="2025-07-22 10:19:29" level=error msg="获取API数据失败 (尝试 2/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 10:19:29" level=warning msg="第 2 次重试获取API数据: research_project"
time="2025-07-22 10:19:31" level=error msg="获取API数据失败 (尝试 3/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 10:19:31" level=warning msg="第 3 次重试获取API数据: research_project"
time="2025-07-22 10:19:34" level=error msg="获取API数据失败 (尝试 4/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 10:19:34" level=warning msg="第 4 次重试获取API数据: research_project"
time="2025-07-22 10:19:38" level=error msg="获取API数据失败 (尝试 5/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 10:19:38" level=warning msg="第 5 次重试获取API数据: research_project"
time="2025-07-22 10:19:43" level=error msg="获取API数据失败 (尝试 6/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 10:19:43" level=error msg="获取API数据失败 [research_project]: 获取第 1 页数据失败: 获取API数据失败，已重试 5 次: HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-22 10:19:43" level=info msg="一次性同步完成: research_project"
time="2025-07-22 10:19:55" level=info msg="日志输出到文件: data_sync.log"
time="2025-07-22 10:19:55" level=info msg="启动 api2mysql v1.0.0"
time="2025-07-22 10:19:55" level=info msg="使用配置文件: config-production.yaml"
time="2025-07-22 10:19:55" level=info msg="执行一次性同步: research_project"
time="2025-07-22 10:19:55" level=info msg="表 research_project 已存在"
time="2025-07-22 10:19:55" level=info msg="开始同步API数据: research_project"
time="2025-07-22 10:19:55" level=info msg="表 research_project 已存在"
time="2025-07-22 10:19:55" level=info msg="开始获取API数据: research_project"
time="2025-07-22 10:19:55" level=info msg="成功获取API数据: research_project，共 133 条记录"
time="2025-07-22 10:19:55" level=info msg="数据转换完成，共处理 133 条记录"
time="2025-07-22 10:19:55" level=error msg="数据同步到数据库失败 [research_project]: 更新插入数据失败: Error 1366: Incorrect decimal value: '' for column 'asset_funding' at row 1"
time="2025-07-22 10:19:55" level=info msg="一次性同步完成: research_project"
