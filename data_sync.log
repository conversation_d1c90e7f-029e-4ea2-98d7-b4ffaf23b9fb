time="2025-07-29 21:00:59" level=info msg="日志输出到文件: data_sync.log"
time="2025-07-29 21:00:59" level=info msg="启动 api2mysql v1.0.0"
time="2025-07-29 21:00:59" level=info msg="使用配置文件: config-production.yaml"
time="2025-07-29 21:00:59" level=fatal msg="创建同步器失败: 创建数据库管理器失败: 确保数据库存在失败: MySQL服务器连接测试失败: dial tcp 10.147.19.17:3306: connect: connection refused"
time="2025-07-29 21:02:09" level=info msg="日志输出到文件: data_sync.log"
time="2025-07-29 21:02:09" level=info msg="启动 api2mysql v1.0.0"
time="2025-07-29 21:02:09" level=info msg="使用配置文件: config-production.yaml"
time="2025-07-29 21:02:09" level=fatal msg="创建同步器失败: 创建数据库管理器失败: 确保数据库存在失败: MySQL服务器连接测试失败: driver: bad connection"
time="2025-07-29 21:03:05" level=info msg="日志输出到文件: data_sync.log"
time="2025-07-29 21:03:05" level=info msg="启动 api2mysql v1.0.0"
time="2025-07-29 21:03:05" level=info msg="使用配置文件: config-production.yaml"
time="2025-07-29 21:03:05" level=fatal msg="创建同步器失败: 创建数据库管理器失败: 确保数据库存在失败: MySQL服务器连接测试失败: driver: bad connection"
