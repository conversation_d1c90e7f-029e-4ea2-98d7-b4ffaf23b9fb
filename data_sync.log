time="2025-06-27 18:22:54" level=info msg="日志输出到文件: data_sync.log"
time="2025-06-27 18:22:54" level=info msg="启动 api2mysql v1.0.0"
time="2025-06-27 18:22:54" level=info msg="使用配置文件: config-production.yaml"
time="2025-06-27 18:22:54" level=info msg="执行一次性同步: department_basic"
time="2025-06-27 18:22:54" level=info msg="创建表: CREATE TABLE `department_basic` (`department_short_name` VARCHAR(100) COMMENT '单位简称', `parent_department_code` VARCHAR(20) COMMENT '隶属单位号', `parent_department_name` VARCHAR(200) COMMENT '隶属单位名称', `department_validity_flag` VARCHAR(10) COMMENT '单位有效标识', `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间', `department_name` VARCHAR(200) NOT NULL COMMENT '单位名称', `department_english_name` VARCHAR(200) COMMENT '单位英文名称', `department_head_name` VARCHAR(100) COMMENT '单位负责人名称', `effective_date` DATE COMMENT '生效日期', `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间', `department_code` VARCHAR(20) PRIMARY KEY COMMENT '单位号', `department_head_code` VARCHAR(20) COMMENT '单位负责人号') ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
time="2025-06-27 18:22:54" level=info msg="表 department_basic 创建成功"
time="2025-06-27 18:22:54" level=info msg="开始同步API数据: department_basic"
time="2025-06-27 18:22:54" level=info msg="表 department_basic 已存在"
time="2025-06-27 18:22:54" level=info msg="开始获取API数据: department_basic"
time="2025-06-27 18:22:54" level=info msg="成功获取API数据: department_basic，共 118 条记录"
time="2025-06-27 18:22:54" level=info msg="数据转换完成，共处理 118 条记录"
time="2025-06-27 18:22:54" level=error msg="数据同步到数据库失败 [department_basic]: 更新插入数据失败: Error 1292: Incorrect date value: '' for column 'effective_date' at row 1"
time="2025-06-27 18:22:54" level=info msg="一次性同步完成: department_basic"
time="2025-06-27 18:31:05" level=info msg="日志输出到文件: data_sync.log"
time="2025-06-27 18:31:05" level=info msg="启动 api2mysql v1.0.0"
time="2025-06-27 18:31:05" level=info msg="使用配置文件: config-production.yaml"
time="2025-06-27 18:31:05" level=info msg="执行一次性同步: department_basic"
time="2025-06-27 18:31:05" level=info msg="创建表: CREATE TABLE `department_basic` (`department_validity_flag` VARCHAR(10) COMMENT '单位有效标识', `department_name` VARCHAR(200) NOT NULL COMMENT '单位名称', `department_english_name` VARCHAR(200) COMMENT '单位英文名称', `department_short_name` VARCHAR(100) COMMENT '单位简称', `parent_department_name` VARCHAR(200) COMMENT '隶属单位名称', `department_head_code` VARCHAR(20) COMMENT '单位负责人号', `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间', `department_code` VARCHAR(20) PRIMARY KEY COMMENT '单位号', `parent_department_code` VARCHAR(20) COMMENT '隶属单位号', `department_head_name` VARCHAR(100) COMMENT '单位负责人名称', `effective_date` VARCHAR(20) COMMENT '生效日期', `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间') ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
time="2025-06-27 18:31:05" level=info msg="表 department_basic 创建成功"
time="2025-06-27 18:31:05" level=info msg="开始同步API数据: department_basic"
time="2025-06-27 18:31:05" level=info msg="表 department_basic 已存在"
time="2025-06-27 18:31:05" level=info msg="开始获取API数据: department_basic"
time="2025-06-27 18:31:05" level=info msg="成功获取API数据: department_basic，共 118 条记录"
time="2025-06-27 18:31:05" level=info msg="数据转换完成，共处理 118 条记录"
time="2025-06-27 18:31:05" level=info msg="成功更新插入 118 条数据到表 department_basic"
time="2025-06-27 18:31:05" level=info msg="API数据同步完成: department_basic，耗时: 294.355083ms，记录数: 118"
time="2025-06-27 18:31:05" level=info msg="一次性同步完成: department_basic"
time="2025-06-27 18:44:02" level=info msg="日志输出到文件: data_sync.log"
time="2025-06-27 18:44:02" level=info msg="启动 api2mysql v1.0.0"
time="2025-06-27 18:44:02" level=info msg="使用配置文件: config-production.yaml"
time="2025-06-27 18:44:02" level=info msg="执行一次性同步: student_basic"
time="2025-06-27 18:44:02" level=info msg="创建表: CREATE TABLE `student_basic` (`college_name` VARCHAR(100) COMMENT '学院名称', `student_status_code` VARCHAR(10) COMMENT '学生当前状态码', `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间', `name_pinyin` VARCHAR(100) COMMENT '姓名拼音', `major_code` VARCHAR(20) COMMENT '专业代码', `class_code` VARCHAR(20) COMMENT '班级代码', `class_name` VARCHAR(100) COMMENT '班级名称', `email` VARCHAR(100) COMMENT '电子邮箱', `student_number` VARCHAR(20) PRIMARY KEY COMMENT '学号', `student_name` VARCHAR(50) NOT NULL COMMENT '学生姓名', `english_name` VARCHAR(100) COMMENT '英文姓名', `college_code` VARCHAR(20) COMMENT '学院代码', `major_name` VARCHAR(100) COMMENT '专业名称', `student_status` VARCHAR(20) COMMENT '学生当前状态', `student_id_number` VARCHAR(30) COMMENT '学籍号', `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间') ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
time="2025-06-27 18:44:02" level=info msg="表 student_basic 创建成功"
time="2025-06-27 18:44:02" level=info msg="开始同步API数据: student_basic"
time="2025-06-27 18:44:02" level=info msg="表 student_basic 已存在"
time="2025-06-27 18:44:02" level=info msg="开始获取API数据: student_basic"
time="2025-06-27 18:44:02" level=info msg="成功获取API数据: student_basic，共 13 条记录"
time="2025-06-27 18:44:02" level=info msg="数据转换完成，共处理 13 条记录"
time="2025-06-27 18:44:02" level=info msg="成功更新插入 13 条数据到表 student_basic"
time="2025-06-27 18:44:02" level=info msg="API数据同步完成: student_basic，耗时: 184.659875ms，记录数: 13"
time="2025-06-27 18:44:02" level=info msg="一次性同步完成: student_basic"
time="2025-06-27 18:54:05" level=info msg="日志输出到文件: data_sync.log"
time="2025-06-27 18:54:05" level=info msg="启动 api2mysql v1.0.0"
time="2025-06-27 18:54:05" level=info msg="使用配置文件: config-production.yaml"
time="2025-06-27 18:54:05" level=info msg="执行一次性同步: teacher_basic"
time="2025-06-27 18:54:05" level=info msg="创建表: CREATE TABLE `teacher_basic` (`updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间', `employee_number` VARCHAR(20) PRIMARY KEY COMMENT '员工工号', `employee_name` VARCHAR(50) NOT NULL COMMENT '员工姓名', `department_code` VARCHAR(20) COMMENT '部门编码', `position_level` VARCHAR(20) COMMENT '职位级别/是否主管', `employment_status` VARCHAR(20) COMMENT '岗位当前状态', `gender` VARCHAR(10) COMMENT '性别', `department_name` VARCHAR(100) COMMENT '部门名称', `job_category` VARCHAR(50) COMMENT '岗位类别', `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间') ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
time="2025-06-27 18:54:05" level=info msg="表 teacher_basic 创建成功"
time="2025-06-27 18:54:05" level=info msg="开始同步API数据: teacher_basic"
time="2025-06-27 18:54:05" level=info msg="表 teacher_basic 已存在"
time="2025-06-27 18:54:05" level=info msg="开始获取API数据: teacher_basic"
time="2025-06-27 18:54:05" level=info msg="成功获取API数据: teacher_basic，共 118 条记录"
time="2025-06-27 18:54:05" level=info msg="数据转换完成，共处理 118 条记录"
time="2025-06-27 18:54:05" level=error msg="数据同步到数据库失败 [teacher_basic]: 更新插入数据失败: Error 1048: Column 'employee_number' cannot be null"
time="2025-06-27 18:54:05" level=info msg="一次性同步完成: teacher_basic"
time="2025-06-27 18:59:29" level=info msg="日志输出到文件: data_sync.log"
time="2025-06-27 18:59:29" level=info msg="启动 api2mysql v1.0.0"
time="2025-06-27 18:59:29" level=info msg="使用配置文件: config-production.yaml"
time="2025-06-27 18:59:29" level=info msg="执行一次性同步: teacher_basic"
time="2025-06-27 18:59:29" level=info msg="表 teacher_basic 已存在"
time="2025-06-27 18:59:29" level=info msg="开始同步API数据: teacher_basic"
time="2025-06-27 18:59:29" level=info msg="表 teacher_basic 已存在"
time="2025-06-27 18:59:29" level=info msg="开始获取API数据: teacher_basic"
time="2025-06-27 18:59:29" level=info msg="成功获取API数据: teacher_basic，共 830 条记录"
time="2025-06-27 18:59:29" level=info msg="数据转换完成，共处理 830 条记录"
time="2025-06-27 18:59:29" level=info msg="成功更新插入 830 条数据到表 teacher_basic"
time="2025-06-27 18:59:29" level=info msg="API数据同步完成: teacher_basic，耗时: 478.231417ms，记录数: 830"
time="2025-06-27 18:59:29" level=info msg="一次性同步完成: teacher_basic"
