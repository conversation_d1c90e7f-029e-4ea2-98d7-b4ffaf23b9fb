time="2025-07-29 21:14:31" level=info msg="日志输出到文件: data_sync.log"
time="2025-07-29 21:14:31" level=info msg="启动 api2mysql v1.0.0"
time="2025-07-29 21:14:31" level=info msg="使用配置文件: config-production.yaml"
time="2025-07-29 21:14:31" level=info msg="DSN without database: root:Bigbang!123@tcp(10.17.58.208:3306)/?charset=utf8mb4&parseTime=True&loc=Local"
time="2025-07-29 21:14:31" level=info msg="数据库 data_sync 已存在"
time="2025-07-29 21:14:31" level=info msg="启动数据同步服务"
time="2025-07-29 21:14:31" level=info msg="表 student_basic 已存在"
time="2025-07-29 21:14:31" level=info msg="表 department_basic 已存在"
time="2025-07-29 21:14:31" level=info msg="表 teacher_basic 已存在"
time="2025-07-29 21:14:31" level=info msg="表 course_schedule 已存在"
time="2025-07-29 21:14:31" level=info msg="表 research_project 已存在"
time="2025-07-29 21:14:31" level=info msg="表 student_status_change 已存在"
time="2025-07-29 21:14:31" level=info msg="表 student_registration 已存在"
time="2025-07-29 21:14:31" level=info msg="数据同步服务已启动"
time="2025-07-29 21:14:31" level=info msg="启动API同步任务: department_basic，同步间隔: 2h0m0s"
time="2025-07-29 21:14:31" level=info msg="启动API同步任务: research_project，同步间隔: 1h0m0s"
time="2025-07-29 21:14:31" level=info msg="启动API同步任务: student_registration，同步间隔: 1h0m0s"
time="2025-07-29 21:14:31" level=info msg="开始同步API数据: department_basic"
time="2025-07-29 21:14:31" level=info msg="开始同步API数据: research_project"
time="2025-07-29 21:14:31" level=info msg="开始同步API数据: student_registration"
time="2025-07-29 21:14:31" level=info msg="启动API同步任务: student_basic，同步间隔: 1h0m0s"
time="2025-07-29 21:14:31" level=info msg="开始同步API数据: student_basic"
time="2025-07-29 21:14:31" level=info msg="启动API同步任务: student_status_change，同步间隔: 1h0m0s"
time="2025-07-29 21:14:31" level=info msg="开始同步API数据: student_status_change"
time="2025-07-29 21:14:31" level=info msg="服务已启动，按 Ctrl+C 停止"
time="2025-07-29 21:14:31" level=info msg="启动API同步任务: course_schedule，同步间隔: 30m0s"
time="2025-07-29 21:14:31" level=info msg="启动API同步任务: teacher_basic，同步间隔: 1h0m0s"
time="2025-07-29 21:14:31" level=info msg="开始同步API数据: course_schedule"
time="2025-07-29 21:14:31" level=info msg="开始同步API数据: teacher_basic"
time="2025-07-29 21:14:31" level=info msg="表 department_basic 已存在"
time="2025-07-29 21:14:31" level=info msg="开始获取API数据: department_basic"
time="2025-07-29 21:14:31" level=info msg="表 student_registration 已存在"
time="2025-07-29 21:14:31" level=info msg="开始获取API数据: student_registration"
time="2025-07-29 21:14:31" level=info msg="表 research_project 已存在"
time="2025-07-29 21:14:31" level=info msg="开始获取API数据: research_project"
time="2025-07-29 21:14:31" level=info msg="表 student_basic 已存在"
time="2025-07-29 21:14:31" level=info msg="开始获取API数据: student_basic"
time="2025-07-29 21:14:31" level=info msg="表 student_status_change 已存在"
time="2025-07-29 21:14:31" level=info msg="开始获取API数据: student_status_change"
time="2025-07-29 21:14:31" level=info msg="表 teacher_basic 已存在"
time="2025-07-29 21:14:31" level=info msg="开始获取API数据: teacher_basic"
time="2025-07-29 21:14:31" level=info msg="开始分页获取数据，每页 100 条记录"
time="2025-07-29 21:14:31" level=info msg="表 course_schedule 已存在"
time="2025-07-29 21:14:31" level=info msg="开始获取API数据: course_schedule"
time="2025-07-29 21:14:31" level=info msg="成功获取API数据: student_registration，共 9 条记录"
time="2025-07-29 21:14:31" level=info msg="数据转换完成，共处理 9 条记录"
time="2025-07-29 21:14:31" level=error msg="获取API数据失败 (尝试 1/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-29 21:14:31" level=warning msg="第 1 次重试获取API数据: teacher_basic"
time="2025-07-29 21:14:31" level=info msg="成功获取API数据: student_status_change，共 8 条记录"
time="2025-07-29 21:14:31" level=info msg="成功获取API数据: student_basic，共 5 条记录"
time="2025-07-29 21:14:31" level=info msg="数据转换完成，共处理 5 条记录"
time="2025-07-29 21:14:31" level=info msg="数据转换完成，共处理 8 条记录"
time="2025-07-29 21:14:31" level=info msg="成功获取API数据: department_basic，共 118 条记录"
time="2025-07-29 21:14:31" level=info msg="数据转换完成，共处理 118 条记录"
time="2025-07-29 21:14:31" level=error msg="获取API数据失败 (尝试 1/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-29 21:14:31" level=warning msg="第 1 次重试获取API数据: course_schedule"
time="2025-07-29 21:14:31" level=info msg="成功更新插入 5 条数据到表 student_basic"
time="2025-07-29 21:14:31" level=info msg="API数据同步完成: student_basic，耗时: 192.562709ms，记录数: 5"
time="2025-07-29 21:14:31" level=info msg="成功更新插入 9 条数据到表 student_registration"
time="2025-07-29 21:14:31" level=info msg="成功更新插入 8 条数据到表 student_status_change"
time="2025-07-29 21:14:31" level=info msg="API数据同步完成: student_status_change，耗时: 195.006875ms，记录数: 8"
time="2025-07-29 21:14:31" level=info msg="API数据同步完成: student_registration，耗时: 197.157334ms，记录数: 9"
time="2025-07-29 21:14:31" level=info msg="成功获取API数据: research_project，共 133 条记录"
time="2025-07-29 21:14:31" level=info msg="数据转换完成，共处理 133 条记录"
time="2025-07-29 21:14:31" level=info msg="成功更新插入 118 条数据到表 department_basic"
time="2025-07-29 21:14:31" level=info msg="API数据同步完成: department_basic，耗时: 252.358042ms，记录数: 118"
time="2025-07-29 21:14:31" level=info msg="成功更新插入 133 条数据到表 research_project"
time="2025-07-29 21:14:31" level=info msg="API数据同步完成: research_project，耗时: 296.7715ms，记录数: 133"
time="2025-07-29 21:14:32" level=error msg="获取API数据失败 (尝试 2/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-29 21:14:32" level=warning msg="第 2 次重试获取API数据: teacher_basic"
time="2025-07-29 21:14:32" level=error msg="获取API数据失败 (尝试 2/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-29 21:14:32" level=warning msg="第 2 次重试获取API数据: course_schedule"
time="2025-07-29 21:14:34" level=error msg="获取API数据失败 (尝试 3/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-29 21:14:34" level=warning msg="第 3 次重试获取API数据: teacher_basic"
time="2025-07-29 21:14:34" level=error msg="获取API数据失败 (尝试 3/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-29 21:14:34" level=warning msg="第 3 次重试获取API数据: course_schedule"
time="2025-07-29 21:14:37" level=error msg="获取API数据失败 (尝试 4/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-29 21:14:37" level=warning msg="第 4 次重试获取API数据: teacher_basic"
time="2025-07-29 21:14:37" level=error msg="获取API数据失败 (尝试 4/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-29 21:14:37" level=warning msg="第 4 次重试获取API数据: course_schedule"
time="2025-07-29 21:14:41" level=error msg="获取API数据失败 (尝试 5/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-29 21:14:41" level=warning msg="第 5 次重试获取API数据: course_schedule"
time="2025-07-29 21:14:41" level=error msg="获取API数据失败 (尝试 5/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-29 21:14:41" level=warning msg="第 5 次重试获取API数据: teacher_basic"
time="2025-07-29 21:14:46" level=error msg="获取API数据失败 (尝试 6/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-29 21:14:46" level=error msg="获取API数据失败 (尝试 6/6): HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-29 21:14:46" level=error msg="获取API数据失败 [course_schedule]: 获取API数据失败，已重试 5 次: HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-29 21:14:46" level=error msg="获取API数据失败 [teacher_basic]: 获取第 1 页数据失败: 获取API数据失败，已重试 5 次: HTTP请求失败，状态码: 404，响应: <html>\r\n<head><title>404 Not Found</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
time="2025-07-29 21:15:01" level=info msg="收到停止信号，正在关闭服务..."
time="2025-07-29 21:15:01" level=info msg="停止数据同步服务"
time="2025-07-29 21:15:01" level=info msg="API同步任务停止: student_basic"
time="2025-07-29 21:15:01" level=info msg="API同步任务停止: teacher_basic"
time="2025-07-29 21:15:01" level=info msg="API同步任务停止: student_status_change"
time="2025-07-29 21:15:01" level=info msg="API同步任务停止: course_schedule"
time="2025-07-29 21:15:01" level=info msg="API同步任务停止: research_project"
time="2025-07-29 21:15:01" level=info msg="API同步任务停止: department_basic"
time="2025-07-29 21:15:01" level=info msg="API同步任务停止: student_registration"
time="2025-07-29 21:15:01" level=info msg="数据同步服务已停止"
time="2025-07-29 21:15:01" level=info msg="服务已停止"
