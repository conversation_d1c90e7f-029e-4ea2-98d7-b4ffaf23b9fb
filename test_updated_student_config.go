package main

import (
	"encoding/json"
	"fmt"
	"log"

	"api2mysql/config"
)

func main() {
	fmt.Println("测试更新后的学生基本信息配置...")

	// 加载配置文件
	cfg, err := config.LoadConfig("config-production.yaml")
	if err != nil {
		log.Fatalf("加载配置文件失败: %v", err)
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		log.Fatalf("配置验证失败: %v", err)
	}

	// 查找学生基本信息API配置
	var studentAPI *config.APIConfig
	for _, api := range cfg.APIs {
		if api.Name == "student_basic" {
			studentAPI = &api
			break
		}
	}

	if studentAPI == nil {
		log.Fatalf("未找到 student_basic API配置")
	}

	fmt.Printf("✅ 找到学生基本信息API配置: %s\n", studentAPI.Description)
	fmt.Printf("📊 配置统计:\n")
	fmt.Printf("  - 字段映射数量: %d\n", len(studentAPI.FieldMapping))
	fmt.Printf("  - 字段类型数量: %d\n", len(studentAPI.FieldTypes))
	fmt.Printf("  - 字段注释数量: %d\n", len(studentAPI.FieldComments))

	// 验证字段映射和字段类型的一致性
	fmt.Println("\n🔍 验证字段映射和类型定义的一致性...")
	missingTypes := []string{}
	missingComments := []string{}

	for apiField, dbField := range studentAPI.FieldMapping {
		// 检查字段类型是否存在
		if _, exists := studentAPI.FieldTypes[dbField]; !exists {
			missingTypes = append(missingTypes, fmt.Sprintf("%s -> %s", apiField, dbField))
		}
		
		// 检查字段注释是否存在
		if _, exists := studentAPI.FieldComments[dbField]; !exists {
			missingComments = append(missingComments, fmt.Sprintf("%s -> %s", apiField, dbField))
		}
	}

	if len(missingTypes) > 0 {
		fmt.Printf("❌ 缺少字段类型定义的字段:\n")
		for _, field := range missingTypes {
			fmt.Printf("  - %s\n", field)
		}
	} else {
		fmt.Printf("✅ 所有映射字段都有对应的类型定义\n")
	}

	if len(missingComments) > 0 {
		fmt.Printf("⚠️  缺少字段注释的字段:\n")
		for _, field := range missingComments {
			fmt.Printf("  - %s\n", field)
		}
	} else {
		fmt.Printf("✅ 所有映射字段都有对应的注释\n")
	}

	// 显示字段映射详情
	fmt.Println("\n📋 字段映射详情:")
	categories := map[string][]string{
		"基本信息": {"student_number", "student_name", "english_name", "name_pinyin", "student_id_number"},
		"学院专业": {"college_code", "college_name", "major_code", "major_name", "class_code", "class_name"},
		"个人信息": {"gender_code", "gender", "birth_date", "ethnicity_code", "ethnicity", "id_type_code", "id_type", "id_number"},
		"联系方式": {"email", "contact_address", "postal_code", "mobile_phone", "qq_number", "wechat_number"},
		"家庭信息": {"family_address", "father_name", "father_occupation", "mother_name", "mother_occupation"},
	}

	for category, fields := range categories {
		fmt.Printf("\n  %s:\n", category)
		for _, dbField := range fields {
			if comment, exists := studentAPI.FieldComments[dbField]; exists {
				if fieldType, typeExists := studentAPI.FieldTypes[dbField]; typeExists {
					fmt.Printf("    %-25s: %s (%s)\n", dbField, comment, fieldType)
				}
			}
		}
	}

	// 模拟JSON数据测试
	fmt.Println("\n🧪 测试JSON数据解析...")
	testJSON := `{
		"xh": "2007217237",
		"xm": "王惠贤",
		"ywxm": "",
		"xmpy": "Wang Hui Xian",
		"yxdm": "300700",
		"yxmc": "航空管理工程学院",
		"zydm": "30070006",
		"zymc": "会计",
		"bjdm": "20072172",
		"bjmc": "20会计二班（扩）",
		"xsdqztm": "01",
		"xsdqzt": "在读",
		"xbm": "2",
		"xb": "女",
		"csrq": "19730318",
		"sfzh": "610422197303181722",
		"mzm": "01",
		"mz": "汉族",
		"zzmmm": "01",
		"zzmm": "中共党员",
		"jkzkm": "10",
		"jkzk": "健康或良好",
		"dzxx": "",
		"txdz": "",
		"jstxh": "",
		"fqxm": "",
		"mqxm": ""
	}`

	var jsonData map[string]interface{}
	if err := json.Unmarshal([]byte(testJSON), &jsonData); err != nil {
		log.Printf("JSON解析失败: %v", err)
		return
	}

	// 模拟数据转换
	transformedData := make(map[string]interface{})
	mappedFields := 0
	
	for apiField, dbField := range studentAPI.FieldMapping {
		if value, exists := jsonData[apiField]; exists {
			transformedData[dbField] = value
			mappedFields++
		}
	}

	fmt.Printf("✅ 成功映射 %d 个字段\n", mappedFields)
	fmt.Printf("📋 转换后的数据示例:\n")
	
	// 显示部分转换结果
	sampleFields := []string{"student_number", "student_name", "college_name", "major_name", "gender", "birth_date"}
	for _, field := range sampleFields {
		if value, exists := transformedData[field]; exists {
			comment := studentAPI.FieldComments[field]
			fmt.Printf("  %-20s: %v (%s)\n", field, value, comment)
		}
	}

	fmt.Println("\n🎉 配置验证完成！")
	fmt.Println("\n📝 下一步:")
	fmt.Println("1. 编译程序: go build -o api2mysql")
	fmt.Println("2. 测试同步: ./api2mysql -config config-production.yaml -sync-once student_basic")
	fmt.Println("3. 检查数据库表结构和数据")
}
