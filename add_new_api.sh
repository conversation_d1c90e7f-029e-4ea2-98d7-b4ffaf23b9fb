#!/bin/bash

# 新增API配置脚本
# 使用方法: ./add_new_api.sh <api_name> <api_path> [table_name]

set -e

# 检查参数
if [ $# -lt 2 ]; then
    echo "使用方法: $0 <api_name> <api_path> [table_name]"
    echo ""
    echo "参数说明:"
    echo "  api_name    - API名称（用作配置中的name字段）"
    echo "  api_path    - API路径（/api/v1/xs/fdm/xxx 部分）"
    echo "  table_name  - 数据库表名（可选，默认使用api_name）"
    echo ""
    echo "示例:"
    echo "  $0 fdm_gxxs_xscjsj /api/v1/xs/fdm/fdm_gxxs_xscjsj student_scores"
    echo "  $0 fdm_gxxs_xskcsj /api/v1/xs/fdm/fdm_gxxs_xskcsj"
    exit 1
fi

API_NAME="$1"
API_PATH="$2"
TABLE_NAME="${3:-$API_NAME}"

# H3C API基础配置
BASE_URL="http://10.89.10.81:33024/1033769420954"
H3C_ID="1033769420954"
H3C_APPKEY="bt2qifdv"

# 配置文件路径
CONFIG_FILE="config-production.yaml"
BACKUP_FILE="config-production.yaml.backup.$(date +%Y%m%d_%H%M%S)"

echo "🚀 开始添加新的API配置..."
echo "API名称: $API_NAME"
echo "API路径: $API_PATH"
echo "表名: $TABLE_NAME"
echo "完整URL: $BASE_URL$API_PATH"
echo ""

# 备份配置文件
echo "📋 备份配置文件到: $BACKUP_FILE"
cp "$CONFIG_FILE" "$BACKUP_FILE"

# 生成新的API配置
NEW_API_CONFIG=$(cat << EOF

  # $API_NAME - $(date +%Y-%m-%d添加)
  - name: "$API_NAME"
    description: "$API_NAME 数据同步"
    url: "$BASE_URL$API_PATH"
    method: "GET"
    headers:
      X-H3C-ID: "$H3C_ID"
      X-H3C-APPKEY: "$H3C_APPKEY"
      Content-Type: "application/json"
    params: {}
    table_name: "$TABLE_NAME"
    field_mapping:
      # TODO: 根据API响应结构配置字段映射
      # 运行测试脚本获取字段结构: go run test_specific_api.go $API_NAME
      id: "id"
    field_types:
      # TODO: 根据数据类型配置字段定义
      id: "VARCHAR(50) PRIMARY KEY"
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    sync_interval: 3600  # 每小时同步一次
    enabled: false  # 配置完成后改为true
EOF
)

# 在template_api配置前插入新配置
sed -i.tmp "/# 预留配置模板/i\\
$NEW_API_CONFIG" "$CONFIG_FILE"

# 删除临时文件
rm -f "$CONFIG_FILE.tmp"

echo "✅ 新API配置已添加到 $CONFIG_FILE"
echo ""
echo "📝 下一步操作:"
echo "1. 运行测试脚本分析API响应结构:"
echo "   go run test_specific_api.go $API_NAME"
echo ""
echo "2. 根据测试结果更新配置文件中的字段映射:"
echo "   - field_mapping: API字段 -> 数据库字段"
echo "   - field_types: 数据库字段类型定义"
echo ""
echo "3. 启用API配置:"
echo "   将 enabled: false 改为 enabled: true"
echo ""
echo "4. 测试同步:"
echo "   ./api2mysql -config $CONFIG_FILE -sync-once $API_NAME"
echo ""
echo "5. 启动持续同步:"
echo "   ./api2mysql -config $CONFIG_FILE"
echo ""
echo "🔄 如需回滚，使用备份文件: $BACKUP_FILE"
