# API to MySQL 数据同步配置文件模板
# 支持环境变量动态配置

# 数据库配置
database:
  host: "${DB_HOST}"
  port: ${DB_PORT}
  username: "${DB_USERNAME}"
  password: "${DB_PASSWORD}"
  database: "${DB_DATABASE}"
  charset: "${DB_CHARSET}"
  max_open_conns: ${DB_MAX_OPEN_CONNS}
  max_idle_conns: ${DB_MAX_IDLE_CONNS}
  conn_max_lifetime: ${DB_CONN_MAX_LIFETIME}  # 秒

# API接口配置
apis:
  # H3C学生基本数据接口
  - name: "student_basic"
    description: "学生基本数据同步"
    url: "https://mock.fangcloud.net/1033769420954/api/v1/fdm/gxxs/fdm_gxxs_xsjbsj"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    table_name: "fdm_gxxs_xsjbsj"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      # 基本信息
      id: "record_id"
      xh: "student_number"
      xm: "student_name"
      ywxm: "english_name"
      xmpy: "name_pinyin"
      
      # 学院专业信息
      yxdm: "college_code"
      yxmc: "college_name"
      zydm: "major_code"
      zymc: "major_name"
      bjdm: "class_code"
      bjmc: "class_name"
      
      # 学生状态
      xsdqztm: "student_status_code"
      xsdqzt: "student_status_name"
      
      # 联系方式
      dzxx: "email"
      xjh: "student_id"
    
    # 根据实际数据类型定义的字段类型
    field_types:
      record_id: "VARCHAR(50) PRIMARY KEY"
      student_number: "VARCHAR(20) NOT NULL"
      student_name: "VARCHAR(50) NOT NULL"
      english_name: "VARCHAR(100)"
      name_pinyin: "VARCHAR(100)"
      college_code: "VARCHAR(20)"
      college_name: "VARCHAR(100)"
      major_code: "VARCHAR(20)"
      major_name: "VARCHAR(100)"
      class_code: "VARCHAR(20)"
      class_name: "VARCHAR(100)"
      student_status_code: "VARCHAR(10)"
      student_status_name: "VARCHAR(30)"
      email: "VARCHAR(100)"
      student_id: "VARCHAR(50)"
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    
    # 字段注释说明
    field_comments:
      record_id: "记录ID"
      student_number: "学号"
      student_name: "学生姓名"
      english_name: "英文姓名"
      name_pinyin: "姓名拼音"
      college_code: "学院代码"
      college_name: "学院名称"
      major_code: "专业代码"
      major_name: "专业名称"
      class_code: "班级代码"
      class_name: "班级名称"
      student_status_code: "学生状态代码"
      student_status_name: "学生状态名称"
      email: "电子邮箱"
      student_id: "学生ID"
      created_at: "记录创建时间"
      updated_at: "记录更新时间"
    
    sync_interval: 3600  # 每小时同步一次
    enabled: true

  # H3C教师基本数据接口
  - name: "teacher_basic"
    description: "教师基本数据同步"
    url: "https://mock.fangcloud.net/1033769420954/api/v1/fdm/gxrs/fdm_gxrs_jsjbsj"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    table_name: "fdm_gxrs_jsjbsj"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      # 基本信息
      id: "record_id"
      zgh: "teacher_number"
      xm: "teacher_name"
      ywxm: "english_name"
      xmpy: "name_pinyin"
      
      # 个人信息
      xbm: "gender_code"
      xb: "gender_name"
      csrq: "birth_date"
      
      # 工作信息
      rzrq: "appointment_date"
      rdsj: "entry_date"
      szdwm: "department_code"
      szdw: "department_name"
      
      # 联系方式
      dzxx: "email"
      sjhm: "phone_number"
      
      # 其他信息
      jlsfsc: "is_record_deleted"
    
    # 根据实际数据类型定义的字段类型
    field_types:
      record_id: "VARCHAR(50) PRIMARY KEY"
      teacher_number: "VARCHAR(20) NOT NULL"
      teacher_name: "VARCHAR(50) NOT NULL"
      english_name: "VARCHAR(100)"
      name_pinyin: "VARCHAR(100)"
      gender_code: "VARCHAR(10)"
      gender_name: "VARCHAR(10)"
      birth_date: "VARCHAR(20)"
      appointment_date: "VARCHAR(20)"
      entry_date: "VARCHAR(20)"
      department_code: "VARCHAR(20)"
      department_name: "VARCHAR(100)"
      email: "VARCHAR(100)"
      phone_number: "VARCHAR(20)"
      is_record_deleted: "VARCHAR(5)"
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    
    # 字段注释说明
    field_comments:
      record_id: "记录ID"
      teacher_number: "教师工号"
      teacher_name: "教师姓名"
      english_name: "英文姓名"
      name_pinyin: "姓名拼音"
      gender_code: "性别代码"
      gender_name: "性别名称"
      birth_date: "出生日期"
      appointment_date: "任职日期"
      entry_date: "入职时间"
      department_code: "所在单位代码"
      department_name: "所在单位名称"
      email: "电子邮箱"
      phone_number: "手机号码"
      is_record_deleted: "记录是否删除"
      created_at: "记录创建时间"
      updated_at: "记录更新时间"
    
    sync_interval: 3600  # 每小时同步一次
    enabled: true

  # H3C院系所单位基本信息接口
  - name: "department_basic"
    description: "院系所单位基本信息同步"
    url: "https://mock.fangcloud.net/1033769420954/api/v1/fdm/gxjg/fdm_gxjg_yxsdwjbxx"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    table_name: "fdm_xx_dw"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      # 基本信息
      id: "record_id"
      dwh: "department_code"
      dwmc: "department_name"
      dwjc: "department_abbreviation"
      ywmc: "english_name"
      lsdwh: "parent_department_code"
      lsdwmc: "parent_department_name"
      
      # 其他信息
      jlsfsc: "is_record_deleted"
    
    # 根据实际数据类型定义的字段类型
    field_types:
      record_id: "VARCHAR(50) PRIMARY KEY"
      department_code: "VARCHAR(20) NOT NULL"
      department_name: "VARCHAR(100) NOT NULL"
      department_abbreviation: "VARCHAR(50)"
      english_name: "VARCHAR(200)"
      parent_department_code: "VARCHAR(20)"
      parent_department_name: "VARCHAR(100)"
      is_record_deleted: "VARCHAR(5)"
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    
    # 字段注释说明
    field_comments:
      record_id: "记录ID"
      department_code: "单位号"
      department_name: "单位名称"
      department_abbreviation: "单位简称"
      english_name: "英文名称"
      parent_department_code: "隶属单位号"
      parent_department_name: "隶属单位名称"
      is_record_deleted: "记录是否删除"
      created_at: "记录创建时间"
      updated_at: "记录更新时间"
    
    sync_interval: 3600  # 每小时同步一次
    enabled: true

# 全局配置
global:
  log_level: "${LOG_LEVEL}"
  log_file: "${LOG_FILE}"
  sync_mode: "${SYNC_MODE}"
  retry_count: ${RETRY_COUNT}
  request_timeout: ${REQUEST_TIMEOUT}  # 秒
