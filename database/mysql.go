package database

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/sirupsen/logrus"

	"api2mysql/config"
)

// MySQLManager MySQL数据库管理器
type MySQLManager struct {
	db     *sql.DB
	config *config.DatabaseConfig
	logger *logrus.Logger
}

// NewMySQLManager 创建MySQL管理器
func NewMySQLManager(cfg *config.DatabaseConfig, logger *logrus.Logger) (*MySQLManager, error) {
	db, err := sql.Open("mysql", cfg.GetDSN())
	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %v", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(cfg.MaxOpenConns)
	db.SetMaxIdleConns(cfg.MaxIdleConns)
	db.SetConnMaxLifetime(time.Duration(cfg.ConnMaxLifetime) * time.Second)

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("数据库连接测试失败: %v", err)
	}

	manager := &MySQLManager{
		db:     db,
		config: cfg,
		logger: logger,
	}

	return manager, nil
}

// Close 关闭数据库连接
func (m *MySQLManager) Close() error {
	if m.db != nil {
		return m.db.Close()
	}
	return nil
}

// CreateTableIfNotExists 创建表（如果不存在）
func (m *MySQLManager) CreateTableIfNotExists(tableName string, fieldTypes map[string]string) error {
	// 检查表是否存在
	exists, err := m.tableExists(tableName)
	if err != nil {
		return fmt.Errorf("检查表是否存在失败: %v", err)
	}

	if exists {
		m.logger.Infof("表 %s 已存在", tableName)
		return nil
	}

	// 构建CREATE TABLE语句
	var fields []string
	for fieldName, fieldType := range fieldTypes {
		fields = append(fields, fmt.Sprintf("`%s` %s", fieldName, fieldType))
	}

	createSQL := fmt.Sprintf("CREATE TABLE `%s` (%s) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
		tableName, strings.Join(fields, ", "))

	m.logger.Infof("创建表: %s", createSQL)

	_, err = m.db.Exec(createSQL)
	if err != nil {
		return fmt.Errorf("创建表失败: %v", err)
	}

	m.logger.Infof("表 %s 创建成功", tableName)
	return nil
}

// tableExists 检查表是否存在
func (m *MySQLManager) tableExists(tableName string) (bool, error) {
	query := "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = ? AND table_name = ?"
	var count int
	err := m.db.QueryRow(query, m.config.Database, tableName).Scan(&count)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// ClearTable 清空表数据
func (m *MySQLManager) ClearTable(tableName string) error {
	query := fmt.Sprintf("DELETE FROM `%s`", tableName)
	_, err := m.db.Exec(query)
	if err != nil {
		return fmt.Errorf("清空表 %s 失败: %v", tableName, err)
	}
	m.logger.Infof("表 %s 数据已清空", tableName)
	return nil
}

// InsertData 插入数据
func (m *MySQLManager) InsertData(tableName string, data []map[string]interface{}) error {
	if len(data) == 0 {
		m.logger.Warnf("没有数据需要插入到表 %s", tableName)
		return nil
	}

	// 获取字段名
	var fields []string
	for field := range data[0] {
		fields = append(fields, fmt.Sprintf("`%s`", field))
	}

	// 构建INSERT语句
	placeholders := strings.Repeat("?,", len(fields))
	placeholders = placeholders[:len(placeholders)-1] // 移除最后一个逗号

	insertSQL := fmt.Sprintf("INSERT INTO `%s` (%s) VALUES (%s)",
		tableName, strings.Join(fields, ","), placeholders)

	// 准备批量插入
	stmt, err := m.db.Prepare(insertSQL)
	if err != nil {
		return fmt.Errorf("准备插入语句失败: %v", err)
	}
	defer stmt.Close()

	// 开始事务
	tx, err := m.db.Begin()
	if err != nil {
		return fmt.Errorf("开始事务失败: %v", err)
	}
	defer tx.Rollback()

	txStmt := tx.Stmt(stmt)
	defer txStmt.Close()

	// 批量插入数据
	for _, row := range data {
		var values []interface{}
		for _, field := range fields {
			fieldName := strings.Trim(field, "`")
			values = append(values, row[fieldName])
		}

		_, err := txStmt.Exec(values...)
		if err != nil {
			return fmt.Errorf("插入数据失败: %v", err)
		}
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}

	m.logger.Infof("成功插入 %d 条数据到表 %s", len(data), tableName)
	return nil
}

// UpsertData 更新插入数据（ON DUPLICATE KEY UPDATE）
func (m *MySQLManager) UpsertData(tableName string, data []map[string]interface{}, primaryKey string) error {
	if len(data) == 0 {
		m.logger.Warnf("没有数据需要更新插入到表 %s", tableName)
		return nil
	}

	// 获取字段名
	var fields []string
	var updateFields []string
	for field := range data[0] {
		fieldName := fmt.Sprintf("`%s`", field)
		fields = append(fields, fieldName)
		if field != primaryKey {
			updateFields = append(updateFields, fmt.Sprintf("%s=VALUES(%s)", fieldName, fieldName))
		}
	}

	// 构建UPSERT语句
	placeholders := strings.Repeat("?,", len(fields))
	placeholders = placeholders[:len(placeholders)-1]

	upsertSQL := fmt.Sprintf("INSERT INTO `%s` (%s) VALUES (%s) ON DUPLICATE KEY UPDATE %s",
		tableName, strings.Join(fields, ","), placeholders, strings.Join(updateFields, ","))

	// 准备批量插入
	stmt, err := m.db.Prepare(upsertSQL)
	if err != nil {
		return fmt.Errorf("准备更新插入语句失败: %v", err)
	}
	defer stmt.Close()

	// 开始事务
	tx, err := m.db.Begin()
	if err != nil {
		return fmt.Errorf("开始事务失败: %v", err)
	}
	defer tx.Rollback()

	txStmt := tx.Stmt(stmt)
	defer txStmt.Close()

	// 批量更新插入数据
	for _, row := range data {
		var values []interface{}
		for _, field := range fields {
			fieldName := strings.Trim(field, "`")
			values = append(values, row[fieldName])
		}

		_, err := txStmt.Exec(values...)
		if err != nil {
			return fmt.Errorf("更新插入数据失败: %v", err)
		}
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}

	m.logger.Infof("成功更新插入 %d 条数据到表 %s", len(data), tableName)
	return nil
}
