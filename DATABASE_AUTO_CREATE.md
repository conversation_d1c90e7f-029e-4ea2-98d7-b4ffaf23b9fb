# 数据库自动创建功能

## 📋 功能概述

新增了数据库自动创建功能，当程序启动时会自动检查目标数据库是否存在，如果不存在则自动创建。

## 🔧 实现原理

### 1. 检查流程
```
启动程序 → 检查数据库是否存在 → 不存在则创建 → 连接数据库 → 继续执行
```

### 2. 核心函数

#### `ensureDatabaseExists`
- **功能**: 确保数据库存在，如果不存在则创建
- **参数**: 数据库配置、日志记录器
- **返回**: 错误信息（如果有）

#### `databaseExists`
- **功能**: 检查数据库是否存在
- **参数**: 数据库连接、数据库名
- **返回**: 是否存在、错误信息

### 3. 实现细节

```go
// 创建不包含数据库名的连接字符串
dsnWithoutDB := fmt.Sprintf("%s:%s@tcp(%s:%d)/?charset=%s&parseTime=True&loc=Local",
    cfg.Username, cfg.Password, cfg.Host, cfg.Port, cfg.Charset)

// 连接到MySQL服务器（不指定数据库）
db, err := sql.Open("mysql", dsnWithoutDB)

// 检查数据库是否存在
query := "SELECT COUNT(*) FROM information_schema.schemata WHERE schema_name = ?"

// 创建数据库
createSQL := fmt.Sprintf("CREATE DATABASE `%s` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci", cfg.Database)
```

## 🚀 使用方法

### 1. 正常启动
程序启动时会自动检查并创建数据库：

```bash
./api2mysql -config config-production.yaml
```

### 2. 日志输出
程序会输出相关日志信息：

```
INFO[0000] 数据库 api2mysql_db 不存在，正在创建...
INFO[0001] 数据库 api2mysql_db 创建成功
```

或者如果数据库已存在：

```
INFO[0000] 数据库 api2mysql_db 已存在
```

## 🧪 测试功能

### 1. 自动测试脚本
使用提供的测试脚本：

```bash
./test_db_creation.sh
```

### 2. 手动测试步骤

1. **删除目标数据库**（如果存在）：
   ```sql
   DROP DATABASE IF EXISTS `your_database_name`;
   ```

2. **运行程序**：
   ```bash
   ./api2mysql -config config-production.yaml -sync-once student_basic
   ```

3. **检查数据库是否被创建**：
   ```sql
   SHOW DATABASES LIKE 'your_database_name';
   ```

## ⚙️ 配置要求

### 1. 数据库权限
确保配置的数据库用户具有以下权限：
- `CREATE` - 创建数据库权限
- `SELECT` - 查询权限（检查数据库是否存在）

### 2. 配置文件
确保 `config-production.yaml` 中的数据库配置正确：

```yaml
database:
  host: "localhost"
  port: 3306
  username: "your_username"
  password: "your_password"
  database: "your_database_name"  # 目标数据库名
  charset: "utf8mb4"
```

## 🔍 错误处理

### 1. 常见错误

#### 权限不足
```
错误: 创建数据库失败: Error 1044: Access denied for user 'username'@'host' to database 'database_name'
```
**解决方案**: 确保数据库用户具有 CREATE 权限

#### 连接失败
```
错误: MySQL服务器连接测试失败: dial tcp: connect: connection refused
```
**解决方案**: 检查数据库服务器地址、端口和网络连接

#### 数据库名冲突
```
错误: 创建数据库失败: Error 1007: Can't create database 'database_name'; database exists
```
**解决方案**: 这通常不会发生，因为程序会先检查数据库是否存在

### 2. 错误日志
所有错误都会记录到日志中，便于排查问题。

## 📝 注意事项

### 1. 安全考虑
- 数据库用户应该只具有必要的权限
- 避免使用具有过高权限的数据库用户

### 2. 性能考虑
- 数据库存在性检查只在程序启动时执行一次
- 不会影响正常的数据同步性能

### 3. 兼容性
- 支持 MySQL 5.7+ 和 MariaDB 10.2+
- 使用标准的 SQL 语句，兼容性良好

## 🔄 升级说明

### 从旧版本升级
如果从没有自动创建功能的版本升级：

1. **无需修改配置文件** - 现有配置文件完全兼容
2. **无需手动创建数据库** - 程序会自动处理
3. **向后兼容** - 如果数据库已存在，程序会正常工作

### 版本兼容性
- ✅ 新安装：自动创建数据库
- ✅ 现有安装：检测到数据库存在，正常工作
- ✅ 配置文件：无需修改

## 🎯 最佳实践

### 1. 部署建议
- 在生产环境部署前，先在测试环境验证功能
- 确保数据库用户权限配置正确
- 监控程序启动日志，确认数据库创建成功

### 2. 监控建议
- 监控数据库创建相关的日志
- 设置告警，当数据库创建失败时及时通知

### 3. 备份建议
- 即使是自动创建的数据库，也要定期备份
- 在重要操作前进行数据备份

## 🔧 故障排除

### 1. 检查清单
- [ ] 数据库服务器是否运行
- [ ] 网络连接是否正常
- [ ] 数据库用户权限是否足够
- [ ] 配置文件格式是否正确
- [ ] 数据库名是否符合命名规范

### 2. 调试方法
- 查看详细日志输出
- 使用测试脚本验证功能
- 手动连接数据库测试权限

这个功能大大简化了部署流程，无需手动创建数据库，提高了系统的易用性和自动化程度。
