# 生产环境API到MySQL数据同步配置文件

# MySQL数据库配置
database:
  host: "localhost"
  port: 3306
  username: "root"
  password: "Bigbang!"
  database: "data_sync"
  charset: "utf8mb4"
  max_open_conns: 20
  max_idle_conns: 10
  conn_max_lifetime: 3600  # 1小时

# API接口配置列表
apis:
  # H3C学生基本数据接口
  - name: "student_basic"
    description: "学生基本数据子类表同步"
    url: "https://mock.fangcloud.net/1033769420954/api/v1/xs/fdm/fdm_gxxs_xsjbsj"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    # 分页参数配置
    params:
      pageSize: "100"  # 每页记录数
      pageNum: "1"     # 起始页码（程序会自动递增）
    table_name: "student_basic"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      # 基本信息
      xh: "student_number"        # 学号
      xm: "student_name"          # 姓名
      ywxm: "english_name"        # 英文姓名
      xmpy: "name_pinyin"         # 姓名拼音
      xjh: "student_id_number"    # 学籍号

      # 学院专业班级信息
      yxdm: "college_code"        # 学院代码
      yxmc: "college_name"        # 学院名称
      zydm: "major_code"          # 专业代码
      zymc: "major_name"          # 专业名称
      bjdm: "class_code"          # 班级代码
      bjmc: "class_name"          # 班级名称

      # 学生状态信息
      xsdqztm: "student_status_code"  # 学生当前状态码
      xsdqzt: "student_status"    # 学生当前状态
      rxztm: "enrollment_status_code"  # 入学状态码
      rxzt: "enrollment_status"   # 入学状态

      # 个人基本信息
      xbm: "gender_code"          # 性别码
      xb: "gender"                # 性别
      csrq: "birth_date"          # 出生日期
      mzm: "ethnicity_code"       # 民族码
      mz: "ethnicity"             # 民族
      sfzjlxm: "id_type_code"     # 身份证件类型码
      sfzjlx: "id_type"           # 身份证件类型
      sfzh: "id_number"           # 身份证号

      # 学历培养信息
      pyccm: "education_level_code"  # 培养层次码
      pycc: "education_level"     # 培养层次
      rxnf: "enrollment_year"     # 入学年份
      xznj: "current_grade"       # 现在年级

      # 政治面貌和健康状况
      zzmmm: "political_status_code"  # 政治面貌码
      zzmm: "political_status"    # 政治面貌
      jkzkm: "health_status_code" # 健康状况码
      jkzk: "health_status"       # 健康状况

      # 联系方式
      dzxx: "email"               # 电子邮箱
      txdz: "contact_address"     # 通讯地址
      yzbm: "postal_code"         # 邮政编码
      jstxh: "mobile_phone"       # 手机号
      qqh: "qq_number"            # QQ号
      wxh: "wechat_number"        # 微信号

      # 家庭信息
      jtzz: "family_address"      # 家庭住址
      fqxm: "father_name"         # 父亲姓名
      fqzw: "father_occupation"   # 父亲职务
      mqxm: "mother_name"         # 母亲姓名
      mqzw: "mother_occupation"   # 母亲职务

      # 录取信息
      rxcj: "admission_score"     # 入学成绩
      lqpcm: "admission_batch_code"  # 录取批次码
      lqpc: "admission_batch"     # 录取批次
      lqlbm: "admission_category_code"  # 录取类别码
      lqlb: "admission_category"  # 录取类别

      # 其他标识
      sfdszn: "is_single_child"   # 是否独生子女
      sfsc: "is_first_generation" # 是否首次
      cym: "source_language_code" # 从语言码
      sfzczxkt: "is_in_system"    # 是否在册在校开通
    # 根据实际数据类型定义的字段类型
    field_types:
      # 基本信息
      student_number: "VARCHAR(20) PRIMARY KEY"
      student_name: "VARCHAR(50) NOT NULL"
      english_name: "VARCHAR(100)"
      name_pinyin: "VARCHAR(100)"
      student_id_number: "VARCHAR(30)"

      # 学院专业班级信息
      college_code: "VARCHAR(20)"
      college_name: "VARCHAR(100)"
      major_code: "VARCHAR(20)"
      major_name: "VARCHAR(100)"
      class_code: "VARCHAR(20)"
      class_name: "VARCHAR(100)"

      # 学生状态信息
      student_status_code: "VARCHAR(10)"
      student_status: "VARCHAR(20)"
      enrollment_status_code: "VARCHAR(10)"
      enrollment_status: "VARCHAR(20)"

      # 个人基本信息
      gender_code: "VARCHAR(5)"
      gender: "VARCHAR(10)"
      birth_date: "VARCHAR(20)"
      ethnicity_code: "VARCHAR(10)"
      ethnicity: "VARCHAR(20)"
      id_type_code: "VARCHAR(10)"
      id_type: "VARCHAR(30)"
      id_number: "VARCHAR(30)"

      # 学历培养信息
      education_level_code: "VARCHAR(10)"
      education_level: "VARCHAR(30)"
      enrollment_year: "VARCHAR(10)"
      current_grade: "VARCHAR(10)"

      # 政治面貌和健康状况
      political_status_code: "VARCHAR(10)"
      political_status: "VARCHAR(30)"
      health_status_code: "VARCHAR(10)"
      health_status: "VARCHAR(30)"

      # 联系方式
      email: "VARCHAR(100)"
      contact_address: "VARCHAR(200)"
      postal_code: "VARCHAR(20)"
      mobile_phone: "VARCHAR(20)"
      qq_number: "VARCHAR(20)"
      wechat_number: "VARCHAR(50)"

      # 家庭信息
      family_address: "VARCHAR(200)"
      father_name: "VARCHAR(50)"
      father_occupation: "VARCHAR(100)"
      mother_name: "VARCHAR(50)"
      mother_occupation: "VARCHAR(100)"

      # 录取信息
      admission_score: "VARCHAR(20)"
      admission_batch_code: "VARCHAR(10)"
      admission_batch: "VARCHAR(50)"
      admission_category_code: "VARCHAR(10)"
      admission_category: "VARCHAR(50)"

      # 其他标识
      is_single_child: "VARCHAR(5)"
      is_first_generation: "VARCHAR(5)"
      source_language_code: "VARCHAR(10)"
      is_in_system: "VARCHAR(5)"

      # 系统字段
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    # 字段注释说明
    field_comments:
      # 基本信息
      student_number: "学号"
      student_name: "学生姓名"
      english_name: "英文姓名"
      name_pinyin: "姓名拼音"
      student_id_number: "学籍号"

      # 学院专业班级信息
      college_code: "学院代码"
      college_name: "学院名称"
      major_code: "专业代码"
      major_name: "专业名称"
      class_code: "班级代码"
      class_name: "班级名称"

      # 学生状态信息
      student_status_code: "学生当前状态码"
      student_status: "学生当前状态"
      enrollment_status_code: "入学状态码"
      enrollment_status: "入学状态"

      # 个人基本信息
      gender_code: "性别码"
      gender: "性别"
      birth_date: "出生日期"
      ethnicity_code: "民族码"
      ethnicity: "民族"
      id_type_code: "身份证件类型码"
      id_type: "身份证件类型"
      id_number: "身份证号"

      # 学历培养信息
      education_level_code: "培养层次码"
      education_level: "培养层次"
      enrollment_year: "入学年份"
      current_grade: "现在年级"

      # 政治面貌和健康状况
      political_status_code: "政治面貌码"
      political_status: "政治面貌"
      health_status_code: "健康状况码"
      health_status: "健康状况"

      # 联系方式
      email: "电子邮箱"
      contact_address: "通讯地址"
      postal_code: "邮政编码"
      mobile_phone: "手机号"
      qq_number: "QQ号"
      wechat_number: "微信号"

      # 家庭信息
      family_address: "家庭住址"
      father_name: "父亲姓名"
      father_occupation: "父亲职务"
      mother_name: "母亲姓名"
      mother_occupation: "母亲职务"

      # 录取信息
      admission_score: "入学成绩"
      admission_batch_code: "录取批次码"
      admission_batch: "录取批次"
      admission_category_code: "录取类别码"
      admission_category: "录取类别"

      # 其他标识
      is_single_child: "是否独生子女"
      is_first_generation: "是否首次"
      source_language_code: "从语言码"
      is_in_system: "是否在册在校开通"

      # 系统字段
      created_at: "记录创建时间"
      updated_at: "记录更新时间"
    sync_interval: 3600  # 每小时同步一次
    enabled: true

  # H3C院系所单位基本信息接口
  - name: "department_basic"
    description: "院系所单位基本信息同步"
    url: "https://mock.fangcloud.net/1033769420954/data_center/xx/v1/fdm_xx_dw"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    # 分页参数配置
    params:
      pageSize: "100"  # 每页记录数
      pageNum: "1"     # 起始页码（程序会自动递增）
    table_name: "department_basic"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      dwh: "department_code"          # 单位号
      dwmc: "department_name"         # 单位名称
      dwywmc: "department_english_name"  # 单位英文名称
      dwjc: "department_short_name"   # 单位简称
      lsdwh: "parent_department_code" # 隶属单位号
      lsdwh_mc: "parent_department_name"  # 隶属单位名称
      dwfzrh: "department_head_code"  # 单位负责人号
      dwfzrh_mc: "department_head_name"   # 单位负责人名称
      sxrq: "effective_date"          # 生效日期
      dwyxbs: "department_validity_flag"  # 单位有效标识
    # 根据实际数据类型定义的字段类型
    field_types:
      department_code: "VARCHAR(20) PRIMARY KEY"
      department_name: "VARCHAR(200) NOT NULL"
      department_english_name: "VARCHAR(200)"
      department_short_name: "VARCHAR(100)"
      parent_department_code: "VARCHAR(20)"
      parent_department_name: "VARCHAR(200)"
      department_head_code: "VARCHAR(20)"
      department_head_name: "VARCHAR(100)"
      effective_date: "VARCHAR(20)"
      department_validity_flag: "VARCHAR(10)"
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    # 字段注释说明
    field_comments:
      department_code: "单位号"
      department_name: "单位名称"
      department_english_name: "单位英文名称"
      department_short_name: "单位简称"
      parent_department_code: "隶属单位号"
      parent_department_name: "隶属单位名称"
      department_head_code: "单位负责人号"
      department_head_name: "单位负责人名称"
      effective_date: "生效日期"
      department_validity_flag: "单位有效标识"
      created_at: "记录创建时间"
      updated_at: "记录更新时间"
    sync_interval: 7200  # 每2小时同步一次（部门信息变化较少）
    enabled: true

  # 员工基本数据接口
  - name: "teacher_basic"
    description: "员工基本数据同步"
    url: "https://mock.fangcloud.net/1033769420954/data_center/jg/v1/fdm_jg_jbxx_lx"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    table_name: "teacher_basic"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      # 基本信息
      gh: "teacher_number"        # 工号
      xm: "teacher_name"          # 姓名
      zjhm: "id_number"           # 证件号码
      xbm: "gender_code"          # 性别码
      xb: "gender"                # 性别
      xbmc: "gender_name"         # 性别名称
      csrq: "birth_date"          # 出生日期

      # 国籍民族信息
      gjm: "country_code"         # 国家码
      gj: "country"               # 国家
      gjmc: "country_name"        # 国家名称
      mzm: "ethnicity_code"       # 民族码
      mz: "ethnicity"             # 民族
      mzmc: "ethnicity_name"      # 民族名称
      jgm: "origin_code"          # 籍贯码
      jg: "origin"                # 籍贯

      # 证件信息
      zjlxm: "id_type_code"       # 证件类型码
      zjlxmc: "id_type_name"      # 证件类型名称

      # 政治面貌
      zzmmm: "political_status_code"    # 政治面貌码
      zzmmmc: "political_status_name"   # 政治面貌名称
      hyzkm: "marital_status_code"      # 婚姻状况码
      hyzkmc: "marital_status_name"     # 婚姻状况名称

      # 工作信息
      prsj: "appointment_date"    # 聘任时间
      rzrq: "entry_date"          # 入职日期
      dwbm: "department_code"     # 单位编码
      dwmc: "department_name"     # 单位名称
      gwlb: "position_category"   # 岗位类别
      ydlx: "mobility_type"       # 异动类型
      sfzb: "is_on_duty"          # 是否在编
      sfssxjs: "is_part_time_teacher"  # 是否双师型教师
      xzzw: "administrative_position"  # 行政职务
      zyjszw: "professional_title_position"  # 专业技术职务
      zc: "professional_title"    # 职称

      # 学历学位信息
      zgxl: "highest_education"   # 最高学历
      zgxw: "highest_degree"      # 最高学位
      byyx: "graduate_school"     # 毕业院校
      sxzy: "major"               # 所学专业

      # 教职工状态
      jzglbm: "staff_category_code"     # 教职工类别码
      jzglbmc: "staff_category_name"    # 教职工类别名称
      jzgdqztm: "current_status_code"   # 教职工当前状态码
      jzgdqztmc: "current_status_name"  # 教职工当前状态名称
      jzgdqzt: "current_status"         # 教职工当前状态
      jzglb: "staff_category"           # 教职工类别
      jzgly: "staff_source"             # 教职工来源

      # 其他信息
      qdhtqk: "contract_situation"      # 签订合同情况
      lxdh: "contact_phone"             # 联系电话
      dzyx: "email"                     # 电子邮箱
      zp: "photo"                       # 照片
      sklx: "subject_category"          # 学科类型
      sfwbkssk: "is_external_lecturer"  # 是否外聘课时授课
      xxjylx: "continuing_education_type"  # 继续教育类型
      bxnsfsk: "is_part_time_teaching"  # 本校内是否授课
      bskyy: "no_teaching_reason"       # 不授课原因
      jxly: "teaching_experience"       # 教学履历
      sffzsszdk: "is_vocational_skills"  # 是否职业技能
    # 根据实际数据类型定义的字段类型
    field_types:
      # 基本信息
      teacher_number: "VARCHAR(20) PRIMARY KEY"
      teacher_name: "VARCHAR(50) NOT NULL"
      id_number: "VARCHAR(30)"
      gender_code: "VARCHAR(5)"
      gender: "VARCHAR(10)"
      gender_name: "VARCHAR(10)"
      birth_date: "DATE"

      # 国籍民族信息
      country_code: "VARCHAR(10)"
      country: "VARCHAR(50)"
      country_name: "VARCHAR(50)"
      ethnicity_code: "VARCHAR(10)"
      ethnicity: "VARCHAR(20)"
      ethnicity_name: "VARCHAR(20)"
      origin_code: "VARCHAR(10)"
      origin: "VARCHAR(50)"

      # 证件信息
      id_type_code: "VARCHAR(10)"
      id_type_name: "VARCHAR(30)"

      # 政治面貌
      political_status_code: "VARCHAR(10)"
      political_status_name: "VARCHAR(30)"
      marital_status_code: "VARCHAR(10)"
      marital_status_name: "VARCHAR(20)"

      # 工作信息
      appointment_date: "DATE"
      entry_date: "DATE"
      department_code: "VARCHAR(20)"
      department_name: "VARCHAR(100)"
      position_category: "VARCHAR(50)"
      mobility_type: "VARCHAR(30)"
      is_on_duty: "VARCHAR(5)"
      is_part_time_teacher: "VARCHAR(5)"
      administrative_position: "VARCHAR(50)"
      professional_title_position: "VARCHAR(50)"
      professional_title: "VARCHAR(50)"

      # 学历学位信息
      highest_education: "VARCHAR(30)"
      highest_degree: "VARCHAR(30)"
      graduate_school: "VARCHAR(100)"
      major: "VARCHAR(100)"

      # 教职工状态
      staff_category_code: "VARCHAR(10)"
      staff_category_name: "VARCHAR(50)"
      current_status_code: "VARCHAR(10)"
      current_status_name: "VARCHAR(30)"
      current_status: "VARCHAR(30)"
      staff_category: "VARCHAR(50)"
      staff_source: "VARCHAR(50)"

      # 其他信息
      contract_situation: "VARCHAR(100)"
      contact_phone: "VARCHAR(20)"
      email: "VARCHAR(100)"
      photo: "TEXT"
      subject_category: "VARCHAR(50)"
      is_external_lecturer: "VARCHAR(5)"
      continuing_education_type: "VARCHAR(50)"
      is_part_time_teaching: "VARCHAR(5)"
      no_teaching_reason: "VARCHAR(100)"
      teaching_experience: "TEXT"
      is_vocational_skills: "VARCHAR(5)"

      # 系统字段
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    # 字段注释说明
    field_comments:
      # 基本信息
      teacher_number: "教师工号"
      teacher_name: "教师姓名"
      id_number: "证件号码"
      gender_code: "性别码"
      gender: "性别"
      gender_name: "性别名称"
      birth_date: "出生日期"

      # 国籍民族信息
      country_code: "国家码"
      country: "国家"
      country_name: "国家名称"
      ethnicity_code: "民族码"
      ethnicity: "民族"
      ethnicity_name: "民族名称"
      origin_code: "籍贯码"
      origin: "籍贯"

      # 证件信息
      id_type_code: "证件类型码"
      id_type_name: "证件类型名称"

      # 政治面貌
      political_status_code: "政治面貌码"
      political_status_name: "政治面貌名称"
      marital_status_code: "婚姻状况码"
      marital_status_name: "婚姻状况名称"

      # 工作信息
      appointment_date: "聘任时间"
      entry_date: "入职日期"
      department_code: "单位编码"
      department_name: "单位名称"
      position_category: "岗位类别"
      mobility_type: "异动类型"
      is_on_duty: "是否在编"
      is_part_time_teacher: "是否双师型教师"
      administrative_position: "行政职务"
      professional_title_position: "专业技术职务"
      professional_title: "职称"

      # 学历学位信息
      highest_education: "最高学历"
      highest_degree: "最高学位"
      graduate_school: "毕业院校"
      major: "所学专业"

      # 教职工状态
      staff_category_code: "教职工类别码"
      staff_category_name: "教职工类别名称"
      current_status_code: "教职工当前状态码"
      current_status_name: "教职工当前状态名称"
      current_status: "教职工当前状态"
      staff_category: "教职工类别"
      staff_source: "教职工来源"

      # 其他信息
      contract_situation: "签订合同情况"
      contact_phone: "联系电话"
      email: "电子邮箱"
      photo: "照片"
      subject_category: "学科类型"
      is_external_lecturer: "是否外聘课时授课"
      continuing_education_type: "继续教育类型"
      is_part_time_teaching: "本校内是否授课"
      no_teaching_reason: "不授课原因"
      teaching_experience: "教学履历"
      is_vocational_skills: "是否职业技能"

      # 系统字段
      created_at: "记录创建时间"
      updated_at: "记录更新时间"
    sync_interval: 3600  # 每小时同步一次
    enabled: true

  
# 全局配置
global:
  # 日志级别: debug, info, warn, error
  log_level: "info"
  # 日志文件路径
  log_file: "data_sync.log"
  # 同步模式: replace（替换）, append（追加）, upsert（更新插入）
  sync_mode: "upsert"  # 推荐使用upsert模式
  # 错误重试次数
  retry_count: 5
  # 请求超时时间（秒）
  request_timeout: 60
