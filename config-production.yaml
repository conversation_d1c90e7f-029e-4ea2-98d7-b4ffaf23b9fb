# 生产环境API到MySQL数据同步配置文件

# MySQL数据库配置
database:
  host: "localhost"
  port: 3306
  username: "root"
  password: "your_password"
  database: "h3c_data_sync"
  charset: "utf8mb4"
  max_open_conns: 20
  max_idle_conns: 10
  conn_max_lifetime: 3600  # 1小时

# API接口配置列表
apis:
  # H3C学生基本数据接口
  - name: "fdm_gxxs_xsjbsj"
    description: "学生基本数据同步"
    url: "http://***********:33024/1033769420954/api/v1/xs/fdm/fdm_gxxs_xsjbsj"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    # 如果API支持分页，可以添加参数
    params:
      # page: "1"
      # pageSize: "1000"
    table_name: "fdm_gxxs_xsjbsj"
    # 字段映射需要根据实际API响应调整
    field_mapping:
      # 示例字段映射，需要根据实际API响应结构调整
      id: "id"
      xh: "student_number"        # 学号
      xm: "student_name"          # 姓名
      xb: "gender"                # 性别
      csrq: "birth_date"          # 出生日期
      sfzh: "id_card"             # 身份证号
      yx: "college"               # 学院
      zy: "major"                 # 专业
      bj: "class_name"            # 班级
      rxrq: "enrollment_date"     # 入学日期
      byzk: "graduation_status"   # 毕业状况
      # 如果有嵌套字段，使用点号分隔
      # contact.phone: "phone"
      # contact.email: "email"
    # 字段类型定义（根据实际数据调整）
    field_types:
      id: "VARCHAR(50) PRIMARY KEY"
      student_number: "VARCHAR(50)"
      student_name: "VARCHAR(100)"
      gender: "VARCHAR(10)"
      birth_date: "DATE"
      id_card: "VARCHAR(20)"
      college: "VARCHAR(100)"
      major: "VARCHAR(100)"
      class_name: "VARCHAR(50)"
      enrollment_date: "DATE"
      graduation_status: "VARCHAR(20)"
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    sync_interval: 3600  # 每小时同步一次
    enabled: true

  # 预留配置模板 - 新增API时复制此模板
  - name: "template_api"
    description: "API配置模板 - 新增时复制此配置"
    url: "http://***********:33024/1033769420954/api/v1/path/to/new/api"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    params: {}
    table_name: "new_table_name"
    field_mapping:
      # 根据新API的响应结构配置字段映射
      id: "id"
      # field1: "db_field1"
      # field2: "db_field2"
    field_types:
      # 根据数据类型配置字段定义
      id: "VARCHAR(50) PRIMARY KEY"
      # db_field1: "VARCHAR(100)"
      # db_field2: "INT"
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    sync_interval: 3600
    enabled: false  # 新增API默认禁用，配置完成后启用

# 全局配置
global:
  # 日志级别: debug, info, warn, error
  log_level: "info"
  # 日志文件路径
  log_file: "h3c-sync.log"
  # 同步模式: replace（替换）, append（追加）, upsert（更新插入）
  sync_mode: "upsert"  # 推荐使用upsert模式
  # 错误重试次数
  retry_count: 5
  # 请求超时时间（秒）
  request_timeout: 60
