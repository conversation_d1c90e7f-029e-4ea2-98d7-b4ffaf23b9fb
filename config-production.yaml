# 生产环境API到MySQL数据同步配置文件

# MySQL数据库配置
database:
  host: "localhost"
  port: 3306
  username: "root"
  password: "your_password"
  database: "h3c_data_sync"
  charset: "utf8mb4"
  max_open_conns: 20
  max_idle_conns: 10
  conn_max_lifetime: 3600  # 1小时

# API接口配置列表
apis:
  # H3C学生基本数据接口
  - name: "fdm_gxxs_xsjbsj"
    description: "学生基本数据子类表同步"
    url: "http://***********:33024/1033769420954/api/v1/xs/fdm/fdm_gxxs_xsjbsj"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    # 如果API支持分页，可以添加参数
    params: {}
    table_name: "fdm_gxxs_xsjbsj"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      xh: "student_number"        # 学号
      xm: "student_name"          # 姓名
      ywxm: "english_name"        # 英文姓名
      xmpy: "name_pinyin"         # 姓名拼音
      yxdm: "college_code"        # 学院代码
      yxmc: "college_name"        # 学院名称
      zydm: "major_code"          # 专业代码
      zymc: "major_name"          # 专业名称
      bjdm: "class_code"          # 班级代码
      bjmc: "class_name"          # 班级名称
      xsdqztm: "student_status_code"  # 学生当前状态码
      xsdqzt: "student_status"    # 学生当前状态
      dzxx: "email"               # 电子邮箱
      xjh: "student_id_number"    # 学籍号
    # 根据实际数据类型定义的字段类型
    field_types:
      student_number: "VARCHAR(20) PRIMARY KEY"  # 学号作为主键
      student_name: "VARCHAR(50) NOT NULL"       # 姓名
      english_name: "VARCHAR(100)"               # 英文姓名
      name_pinyin: "VARCHAR(100)"                # 姓名拼音
      college_code: "VARCHAR(20)"                # 学院代码
      college_name: "VARCHAR(100)"               # 学院名称
      major_code: "VARCHAR(20)"                  # 专业代码
      major_name: "VARCHAR(100)"                 # 专业名称
      class_code: "VARCHAR(20)"                  # 班级代码
      class_name: "VARCHAR(100)"                 # 班级名称
      student_status_code: "VARCHAR(10)"         # 学生状态码
      student_status: "VARCHAR(20)"              # 学生状态
      email: "VARCHAR(100)"                      # 电子邮箱
      student_id_number: "VARCHAR(30)"           # 学籍号
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    sync_interval: 3600  # 每小时同步一次
    enabled: true

  # H3C院系所单位基本信息接口
  - name: "fdm_xx_dw"
    description: "院系所单位基本信息同步"
    url: "http://***********:33024/1033769420954/data_center/xx/v1/fdm_xx_dw"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    params: {}
    table_name: "fdm_xx_dw"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      dwh: "department_code"          # 单位号
      dwmc: "department_name"         # 单位名称
      dwywmc: "department_english_name"  # 单位英文名称
      dwjc: "department_short_name"   # 单位简称
      lsdwh: "parent_department_code" # 隶属单位号
      lsdwh_mc: "parent_department_name"  # 隶属单位名称
      dwfzrh: "department_head_code"  # 单位负责人号
      dwfzrh_mc: "department_head_name"   # 单位负责人名称
      sxrq: "effective_date"          # 生效日期
      dwyxbs: "department_validity_flag"  # 单位有效标识
    # 根据实际数据类型定义的字段类型
    field_types:
      department_code: "VARCHAR(20) PRIMARY KEY"     # 单位号作为主键
      department_name: "VARCHAR(200) NOT NULL"       # 单位名称
      department_english_name: "VARCHAR(200)"        # 单位英文名称
      department_short_name: "VARCHAR(100)"          # 单位简称
      parent_department_code: "VARCHAR(20)"          # 隶属单位号
      parent_department_name: "VARCHAR(200)"         # 隶属单位名称
      department_head_code: "VARCHAR(20)"            # 单位负责人号
      department_head_name: "VARCHAR(100)"           # 单位负责人名称
      effective_date: "DATE"                         # 生效日期
      department_validity_flag: "VARCHAR(10)"        # 单位有效标识
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    sync_interval: 7200  # 每2小时同步一次（部门信息变化较少）
    enabled: true

  # 预留配置模板 - 新增API时复制此模板
  - name: "template_api"
    description: "API配置模板 - 新增时复制此配置"
    url: "http://***********:33024/1033769420954/api/v1/path/to/new/api"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    params: {}
    table_name: "new_table_name"
    field_mapping:
      # 根据新API的响应结构配置字段映射
      id: "id"
      # field1: "db_field1"
      # field2: "db_field2"
    field_types:
      # 根据数据类型配置字段定义
      id: "VARCHAR(50) PRIMARY KEY"
      # db_field1: "VARCHAR(100)"
      # db_field2: "INT"
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    sync_interval: 3600
    enabled: false  # 新增API默认禁用，配置完成后启用

# 全局配置
global:
  # 日志级别: debug, info, warn, error
  log_level: "info"
  # 日志文件路径
  log_file: "h3c-sync.log"
  # 同步模式: replace（替换）, append（追加）, upsert（更新插入）
  sync_mode: "upsert"  # 推荐使用upsert模式
  # 错误重试次数
  retry_count: 5
  # 请求超时时间（秒）
  request_timeout: 60
