# 生产环境API到MySQL数据同步配置文件

# MySQL数据库配置
database:
  host: "localhost"
  port: 3306
  username: "root"
  password: "Bigbang!"
  database: "data_sync"
  charset: "utf8mb4"
  max_open_conns: 20
  max_idle_conns: 10
  conn_max_lifetime: 3600  # 1小时

# API接口配置列表
apis:
  # H3C学生基本数据接口
  - name: "student_basic"
    description: "学生基本数据子类表同步"
    url: "https://mock.fangcloud.net/1033769420954/api/v1/xs/fdm/fdm_gxxs_xsjbsj"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    # 分页参数配置
    params:
      pageSize: "100"  # 每页记录数
      pageNum: "1"     # 起始页码（程序会自动递增）
    table_name: "student_basic"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      # 基本信息
      xh: "student_number"        # 学号
      xm: "student_name"          # 姓名
      ywxm: "english_name"        # 英文姓名
      xmpy: "name_pinyin"         # 姓名拼音
      xjh: "student_id_number"    # 学籍号

      # 学院专业班级信息
      yxdm: "college_code"        # 学院代码
      yxmc: "college_name"        # 学院名称
      zydm: "major_code"          # 专业代码
      zymc: "major_name"          # 专业名称
      bjdm: "class_code"          # 班级代码
      bjmc: "class_name"          # 班级名称

      # 学生状态信息
      xsdqztm: "student_status_code"  # 学生当前状态码
      xsdqzt: "student_status"    # 学生当前状态
      rxztm: "enrollment_status_code"  # 入学状态码
      rxzt: "enrollment_status"   # 入学状态

      # 个人基本信息
      xbm: "gender_code"          # 性别码
      xb: "gender"                # 性别
      csrq: "birth_date"          # 出生日期
      mzm: "ethnicity_code"       # 民族码
      mz: "ethnicity"             # 民族
      sfzjlxm: "id_type_code"     # 身份证件类型码
      sfzjlx: "id_type"           # 身份证件类型
      sfzh: "id_number"           # 身份证号

      # 学历培养信息
      pyccm: "education_level_code"  # 培养层次码
      pycc: "education_level"     # 培养层次
      rxnf: "enrollment_year"     # 入学年份
      xznj: "current_grade"       # 现在年级

      # 政治面貌和健康状况
      zzmmm: "political_status_code"  # 政治面貌码
      zzmm: "political_status"    # 政治面貌
      jkzkm: "health_status_code" # 健康状况码
      jkzk: "health_status"       # 健康状况

      # 联系方式
      dzxx: "email"               # 电子邮箱
      txdz: "contact_address"     # 通讯地址
      yzbm: "postal_code"         # 邮政编码
      jstxh: "mobile_phone"       # 手机号
      qqh: "qq_number"            # QQ号
      wxh: "wechat_number"        # 微信号

      # 家庭信息
      jtzz: "family_address"      # 家庭住址
      fqxm: "father_name"         # 父亲姓名
      fqzw: "father_occupation"   # 父亲职务
      mqxm: "mother_name"         # 母亲姓名
      mqzw: "mother_occupation"   # 母亲职务

      # 录取信息
      rxcj: "admission_score"     # 入学成绩
      lqpcm: "admission_batch_code"  # 录取批次码
      lqpc: "admission_batch"     # 录取批次
      lqlbm: "admission_category_code"  # 录取类别码
      lqlb: "admission_category"  # 录取类别

      # 其他标识
      sfdszn: "is_single_child"   # 是否独生子女
      sfsc: "is_first_generation" # 是否首次
      cym: "source_language_code" # 从语言码
      sfzczxkt: "is_in_system"    # 是否在册在校开通
    # 根据实际数据类型定义的字段类型
    field_types:
      # 基本信息
      student_number: "VARCHAR(20) PRIMARY KEY"
      student_name: "VARCHAR(50) NOT NULL"
      english_name: "VARCHAR(100)"
      name_pinyin: "VARCHAR(100)"
      student_id_number: "VARCHAR(30)"

      # 学院专业班级信息
      college_code: "VARCHAR(20)"
      college_name: "VARCHAR(100)"
      major_code: "VARCHAR(20)"
      major_name: "VARCHAR(100)"
      class_code: "VARCHAR(20)"
      class_name: "VARCHAR(100)"

      # 学生状态信息
      student_status_code: "VARCHAR(10)"
      student_status: "VARCHAR(20)"
      enrollment_status_code: "VARCHAR(10)"
      enrollment_status: "VARCHAR(20)"

      # 个人基本信息
      gender_code: "VARCHAR(5)"
      gender: "VARCHAR(10)"
      birth_date: "VARCHAR(20)"
      ethnicity_code: "VARCHAR(10)"
      ethnicity: "VARCHAR(20)"
      id_type_code: "VARCHAR(10)"
      id_type: "VARCHAR(30)"
      id_number: "VARCHAR(30)"

      # 学历培养信息
      education_level_code: "VARCHAR(10)"
      education_level: "VARCHAR(30)"
      enrollment_year: "VARCHAR(10)"
      current_grade: "VARCHAR(10)"

      # 政治面貌和健康状况
      political_status_code: "VARCHAR(10)"
      political_status: "VARCHAR(30)"
      health_status_code: "VARCHAR(10)"
      health_status: "VARCHAR(30)"

      # 联系方式
      email: "VARCHAR(100)"
      contact_address: "VARCHAR(200)"
      postal_code: "VARCHAR(20)"
      mobile_phone: "VARCHAR(20)"
      qq_number: "VARCHAR(20)"
      wechat_number: "VARCHAR(50)"

      # 家庭信息
      family_address: "VARCHAR(200)"
      father_name: "VARCHAR(50)"
      father_occupation: "VARCHAR(100)"
      mother_name: "VARCHAR(50)"
      mother_occupation: "VARCHAR(100)"

      # 录取信息
      admission_score: "VARCHAR(20)"
      admission_batch_code: "VARCHAR(10)"
      admission_batch: "VARCHAR(50)"
      admission_category_code: "VARCHAR(10)"
      admission_category: "VARCHAR(50)"

      # 其他标识
      is_single_child: "VARCHAR(5)"
      is_first_generation: "VARCHAR(5)"
      source_language_code: "VARCHAR(10)"
      is_in_system: "VARCHAR(5)"

      # 系统字段
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    # 字段注释说明
    field_comments:
      # 基本信息
      student_number: "学号"
      student_name: "学生姓名"
      english_name: "英文姓名"
      name_pinyin: "姓名拼音"
      student_id_number: "学籍号"

      # 学院专业班级信息
      college_code: "学院代码"
      college_name: "学院名称"
      major_code: "专业代码"
      major_name: "专业名称"
      class_code: "班级代码"
      class_name: "班级名称"

      # 学生状态信息
      student_status_code: "学生当前状态码"
      student_status: "学生当前状态"
      enrollment_status_code: "入学状态码"
      enrollment_status: "入学状态"

      # 个人基本信息
      gender_code: "性别码"
      gender: "性别"
      birth_date: "出生日期"
      ethnicity_code: "民族码"
      ethnicity: "民族"
      id_type_code: "身份证件类型码"
      id_type: "身份证件类型"
      id_number: "身份证号"

      # 学历培养信息
      education_level_code: "培养层次码"
      education_level: "培养层次"
      enrollment_year: "入学年份"
      current_grade: "现在年级"

      # 政治面貌和健康状况
      political_status_code: "政治面貌码"
      political_status: "政治面貌"
      health_status_code: "健康状况码"
      health_status: "健康状况"

      # 联系方式
      email: "电子邮箱"
      contact_address: "通讯地址"
      postal_code: "邮政编码"
      mobile_phone: "手机号"
      qq_number: "QQ号"
      wechat_number: "微信号"

      # 家庭信息
      family_address: "家庭住址"
      father_name: "父亲姓名"
      father_occupation: "父亲职务"
      mother_name: "母亲姓名"
      mother_occupation: "母亲职务"

      # 录取信息
      admission_score: "入学成绩"
      admission_batch_code: "录取批次码"
      admission_batch: "录取批次"
      admission_category_code: "录取类别码"
      admission_category: "录取类别"

      # 其他标识
      is_single_child: "是否独生子女"
      is_first_generation: "是否首次"
      source_language_code: "从语言码"
      is_in_system: "是否在册在校开通"

      # 系统字段
      created_at: "记录创建时间"
      updated_at: "记录更新时间"
    sync_interval: 3600  # 每小时同步一次
    enabled: true

  # H3C院系所单位基本信息接口
  - name: "department_basic"
    description: "院系所单位基本信息同步"
    url: "https://mock.fangcloud.net/1033769420954/data_center/xx/v1/fdm_xx_dw"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    # 分页参数配置
    params:
      pageSize: "100"  # 每页记录数
      pageNum: "1"     # 起始页码（程序会自动递增）
    table_name: "department_basic"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      dwh: "department_code"          # 单位号
      dwmc: "department_name"         # 单位名称
      dwywmc: "department_english_name"  # 单位英文名称
      dwjc: "department_short_name"   # 单位简称
      lsdwh: "parent_department_code" # 隶属单位号
      lsdwh_mc: "parent_department_name"  # 隶属单位名称
      dwfzrh: "department_head_code"  # 单位负责人号
      dwfzrh_mc: "department_head_name"   # 单位负责人名称
      sxrq: "effective_date"          # 生效日期
      dwyxbs: "department_validity_flag"  # 单位有效标识
    # 根据实际数据类型定义的字段类型
    field_types:
      department_code: "VARCHAR(20) PRIMARY KEY"
      department_name: "VARCHAR(200) NOT NULL"
      department_english_name: "VARCHAR(200)"
      department_short_name: "VARCHAR(100)"
      parent_department_code: "VARCHAR(20)"
      parent_department_name: "VARCHAR(200)"
      department_head_code: "VARCHAR(20)"
      department_head_name: "VARCHAR(100)"
      effective_date: "VARCHAR(20)"
      department_validity_flag: "VARCHAR(10)"
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    # 字段注释说明
    field_comments:
      department_code: "单位号"
      department_name: "单位名称"
      department_english_name: "单位英文名称"
      department_short_name: "单位简称"
      parent_department_code: "隶属单位号"
      parent_department_name: "隶属单位名称"
      department_head_code: "单位负责人号"
      department_head_name: "单位负责人名称"
      effective_date: "生效日期"
      department_validity_flag: "单位有效标识"
      created_at: "记录创建时间"
      updated_at: "记录更新时间"
    sync_interval: 7200  # 每2小时同步一次（部门信息变化较少）
    enabled: true

  # 员工基本数据接口
  - name: "teacher_basic"
    description: "员工基本数据同步"
    url: "https://mock.fangcloud.net/1033769420954/data_center/jg/v1/fdm_jg_jbxx_lx"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    # 分页参数配置
    params:
      pageSize: "100"  # 每页记录数
      pageNum: "1"     # 起始页码（程序会自动递增）
    table_name: "teacher_basic"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      # 基本信息
      gh: "teacher_number"        # 工号
      xm: "teacher_name"          # 姓名
      zjhm: "id_number"           # 证件号码
      xbm: "gender_code"          # 性别码
      xb: "gender"                # 性别
      xbmc: "gender_name"         # 性别名称
      csrq: "birth_date"          # 出生日期

      # 国籍民族信息
      gjm: "country_code"         # 国家码
      gj: "country"               # 国家
      gjmc: "country_name"        # 国家名称
      mzm: "ethnicity_code"       # 民族码
      mz: "ethnicity"             # 民族
      mzmc: "ethnicity_name"      # 民族名称
      jgm: "origin_code"          # 籍贯码
      jg: "origin"                # 籍贯

      # 证件信息
      zjlxm: "id_type_code"       # 证件类型码
      zjlxmc: "id_type_name"      # 证件类型名称

      # 政治面貌
      zzmmm: "political_status_code"    # 政治面貌码
      zzmmmc: "political_status_name"   # 政治面貌名称
      hyzkm: "marital_status_code"      # 婚姻状况码
      hyzkmc: "marital_status_name"     # 婚姻状况名称

      # 工作信息
      prsj: "appointment_date"    # 聘任时间
      rzrq: "entry_date"          # 入职日期
      dwbm: "department_code"     # 单位编码
      dwmc: "department_name"     # 单位名称
      gwlb: "position_category"   # 岗位类别
      ydlx: "mobility_type"       # 异动类型
      sfzb: "is_on_duty"          # 是否在编
      sfssxjs: "is_part_time_teacher"  # 是否双师型教师
      xzzw: "administrative_position"  # 行政职务
      zyjszw: "professional_title_position"  # 专业技术职务
      zc: "professional_title"    # 职称

      # 学历学位信息
      zgxl: "highest_education"   # 最高学历
      zgxw: "highest_degree"      # 最高学位
      byyx: "graduate_school"     # 毕业院校
      sxzy: "major"               # 所学专业

      # 教职工状态
      jzglbm: "staff_category_code"     # 教职工类别码
      jzglbmc: "staff_category_name"    # 教职工类别名称
      jzgdqztm: "current_status_code"   # 教职工当前状态码
      jzgdqztmc: "current_status_name"  # 教职工当前状态名称
      jzgdqzt: "current_status"         # 教职工当前状态
      jzglb: "staff_category"           # 教职工类别
      jzgly: "staff_source"             # 教职工来源

      # 其他信息
      qdhtqk: "contract_situation"      # 签订合同情况
      lxdh: "contact_phone"             # 联系电话
      dzyx: "email"                     # 电子邮箱
      zp: "photo"                       # 照片
      sklx: "subject_category"          # 学科类型
      sfwbkssk: "is_external_lecturer"  # 是否外聘课时授课
      xxjylx: "continuing_education_type"  # 继续教育类型
      bxnsfsk: "is_part_time_teaching"  # 本校内是否授课
      bskyy: "no_teaching_reason"       # 不授课原因
      jxly: "teaching_experience"       # 教学履历
      sffzsszdk: "is_vocational_skills"  # 是否职业技能
    # 根据实际数据类型定义的字段类型
    field_types:
      # 基本信息
      teacher_number: "VARCHAR(20) PRIMARY KEY"
      teacher_name: "VARCHAR(50) NOT NULL"
      id_number: "VARCHAR(30)"
      gender_code: "VARCHAR(5)"
      gender: "VARCHAR(10)"
      gender_name: "VARCHAR(10)"
      birth_date: "DATE"

      # 国籍民族信息
      country_code: "VARCHAR(10)"
      country: "VARCHAR(50)"
      country_name: "VARCHAR(50)"
      ethnicity_code: "VARCHAR(10)"
      ethnicity: "VARCHAR(20)"
      ethnicity_name: "VARCHAR(20)"
      origin_code: "VARCHAR(10)"
      origin: "VARCHAR(50)"

      # 证件信息
      id_type_code: "VARCHAR(10)"
      id_type_name: "VARCHAR(30)"

      # 政治面貌
      political_status_code: "VARCHAR(10)"
      political_status_name: "VARCHAR(30)"
      marital_status_code: "VARCHAR(10)"
      marital_status_name: "VARCHAR(20)"

      # 工作信息
      appointment_date: "DATE"
      entry_date: "DATE"
      department_code: "VARCHAR(20)"
      department_name: "VARCHAR(100)"
      position_category: "VARCHAR(50)"
      mobility_type: "VARCHAR(30)"
      is_on_duty: "VARCHAR(5)"
      is_part_time_teacher: "VARCHAR(5)"
      administrative_position: "VARCHAR(50)"
      professional_title_position: "VARCHAR(50)"
      professional_title: "VARCHAR(50)"

      # 学历学位信息
      highest_education: "VARCHAR(30)"
      highest_degree: "VARCHAR(30)"
      graduate_school: "VARCHAR(100)"
      major: "VARCHAR(100)"

      # 教职工状态
      staff_category_code: "VARCHAR(10)"
      staff_category_name: "VARCHAR(50)"
      current_status_code: "VARCHAR(10)"
      current_status_name: "VARCHAR(30)"
      current_status: "VARCHAR(30)"
      staff_category: "VARCHAR(50)"
      staff_source: "VARCHAR(50)"

      # 其他信息
      contract_situation: "VARCHAR(100)"
      contact_phone: "VARCHAR(20)"
      email: "VARCHAR(100)"
      photo: "TEXT"
      subject_category: "VARCHAR(50)"
      is_external_lecturer: "VARCHAR(5)"
      continuing_education_type: "VARCHAR(50)"
      is_part_time_teaching: "VARCHAR(5)"
      no_teaching_reason: "VARCHAR(100)"
      teaching_experience: "TEXT"
      is_vocational_skills: "VARCHAR(5)"

      # 系统字段
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    # 字段注释说明
    field_comments:
      # 基本信息
      teacher_number: "教师工号"
      teacher_name: "教师姓名"
      id_number: "证件号码"
      gender_code: "性别码"
      gender: "性别"
      gender_name: "性别名称"
      birth_date: "出生日期"

      # 国籍民族信息
      country_code: "国家码"
      country: "国家"
      country_name: "国家名称"
      ethnicity_code: "民族码"
      ethnicity: "民族"
      ethnicity_name: "民族名称"
      origin_code: "籍贯码"
      origin: "籍贯"

      # 证件信息
      id_type_code: "证件类型码"
      id_type_name: "证件类型名称"

      # 政治面貌
      political_status_code: "政治面貌码"
      political_status_name: "政治面貌名称"
      marital_status_code: "婚姻状况码"
      marital_status_name: "婚姻状况名称"

      # 工作信息
      appointment_date: "聘任时间"
      entry_date: "入职日期"
      department_code: "单位编码"
      department_name: "单位名称"
      position_category: "岗位类别"
      mobility_type: "异动类型"
      is_on_duty: "是否在编"
      is_part_time_teacher: "是否双师型教师"
      administrative_position: "行政职务"
      professional_title_position: "专业技术职务"
      professional_title: "职称"

      # 学历学位信息
      highest_education: "最高学历"
      highest_degree: "最高学位"
      graduate_school: "毕业院校"
      major: "所学专业"

      # 教职工状态
      staff_category_code: "教职工类别码"
      staff_category_name: "教职工类别名称"
      current_status_code: "教职工当前状态码"
      current_status_name: "教职工当前状态名称"
      current_status: "教职工当前状态"
      staff_category: "教职工类别"
      staff_source: "教职工来源"

      # 其他信息
      contract_situation: "签订合同情况"
      contact_phone: "联系电话"
      email: "电子邮箱"
      photo: "照片"
      subject_category: "学科类型"
      is_external_lecturer: "是否外聘课时授课"
      continuing_education_type: "继续教育类型"
      is_part_time_teaching: "本校内是否授课"
      no_teaching_reason: "不授课原因"
      teaching_experience: "教学履历"
      is_vocational_skills: "是否职业技能"

      # 系统字段
      created_at: "记录创建时间"
      updated_at: "记录更新时间"
    sync_interval: 3600  # 每小时同步一次
    enabled: true

  # H3C课程数据表接口
  - name: "course_schedule"
    description: "课程数据表同步"
    url: "https://mock.fangcloud.net/api/v1/fdm/gxjx/fdm_gxjx_kbsj"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    # 分页参数配置
    params:
      pageSize: "100"  # 每页记录数
      pageNum: "1"     # 起始页码（程序会自动递增）
    table_name: "course_schedule"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      # 基本标识信息
      apid: "schedule_id"         # 排课ID
      xnxqid: "semester_id"       # 学年学期ID
      xnxq: "semester"            # 学年学期

      # 开课信息
      kkd: "course_location_code" # 开课地点代码
      kkdlb: "course_location_type" # 开课地点类别
      sjbz: "time_flag"           # 时间标志
      kkzc: "course_weeks"        # 开课周次
      kkzcmx: "course_weeks_detail" # 开课周次明细

      # 课程时间信息
      kcsjbh: "course_time_code"  # 课程时间编号
      kcsj: "course_time"         # 课程时间
      kcsjmx: "course_time_detail" # 课程时间明细
      kssj: "start_time"          # 开始时间
      jssj: "end_time"            # 结束时间
      xq: "weekday"               # 星期

      # 排课信息
      pklb: "schedule_category"   # 排课类别
      dkbz: "substitute_flag"     # 代课标志
      dkip: "substitute_ip"       # 代课IP
      dkrjsid: "substitute_teacher_id" # 代课教师ID

      # 教师信息
      jsid: "teacher_id"          # 教师ID
      oldjsid: "old_teacher_id"   # 原教师ID
      jlsfsc: "is_record_deleted" # 记录是否删除

      # 课表基础信息
      kbjcmsid: "course_basic_id" # 课表基础描述ID

      # 操作信息
      czr: "operator"             # 操作人
      czsj: "operation_time"      # 操作时间
      cjsj: "create_time"         # 创建时间
      gxsj: "update_time"         # 更新时间

      # 排课班级
      pkbj: "schedule_class"      # 排课班级

      # 录播相关
      lpyy: "recording_reason"    # 录播原因
      ljmc: "link_name"           # 链接名称
      ewmmc: "qr_code_name"       # 二维码名称
      ewmdz: "qr_code_address"    # 二维码地址
      ljlx: "link_type"           # 链接类型

      # 实验室相关
      sftssy: "is_special_lab"    # 是否特殊实验室
      syyyid: "lab_reason_id"     # 实验室原因ID

      # 上课时间
      skkssj: "actual_start_time" # 实际开始时间
      skjssj: "actual_end_time"   # 实际结束时间

    # 根据实际数据类型定义的字段类型
    field_types:
      # 基本标识信息
      schedule_id: "VARCHAR(50) PRIMARY KEY"
      semester_id: "VARCHAR(20)"
      semester: "VARCHAR(20)"

      # 开课信息
      course_location_code: "VARCHAR(10)"
      course_location_type: "VARCHAR(20)"
      time_flag: "VARCHAR(20)"
      course_weeks: "VARCHAR(100)"
      course_weeks_detail: "VARCHAR(200)"

      # 课程时间信息
      course_time_code: "VARCHAR(20)"
      course_time: "VARCHAR(20)"
      course_time_detail: "VARCHAR(100)"
      start_time: "TIME"
      end_time: "TIME"
      weekday: "VARCHAR(5)"

      # 排课信息
      schedule_category: "VARCHAR(50)"
      substitute_flag: "VARCHAR(10)"
      substitute_ip: "VARCHAR(50)"
      substitute_teacher_id: "VARCHAR(50)"

      # 教师信息
      teacher_id: "VARCHAR(50)"
      old_teacher_id: "VARCHAR(50)"
      is_record_deleted: "VARCHAR(5)"

      # 课表基础信息
      course_basic_id: "VARCHAR(50)"

      # 操作信息
      operator: "VARCHAR(50)"
      operation_time: "DATETIME"
      create_time: "DATETIME"
      update_time: "DATETIME"

      # 排课班级
      schedule_class: "VARCHAR(100)"

      # 录播相关
      recording_reason: "VARCHAR(100)"
      link_name: "VARCHAR(100)"
      qr_code_name: "VARCHAR(100)"
      qr_code_address: "VARCHAR(200)"
      link_type: "VARCHAR(20)"

      # 实验室相关
      is_special_lab: "VARCHAR(5)"
      lab_reason_id: "VARCHAR(50)"

      # 上课时间
      actual_start_time: "TIME"
      actual_end_time: "TIME"

      # 系统字段
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"

    # 字段注释说明
    field_comments:
      # 基本标识信息
      schedule_id: "排课ID"
      semester_id: "学年学期ID"
      semester: "学年学期"

      # 开课信息
      course_location_code: "开课地点代码"
      course_location_type: "开课地点类别"
      time_flag: "时间标志"
      course_weeks: "开课周次"
      course_weeks_detail: "开课周次明细"

      # 课程时间信息
      course_time_code: "课程时间编号"
      course_time: "课程时间"
      course_time_detail: "课程时间明细"
      start_time: "开始时间"
      end_time: "结束时间"
      weekday: "星期"

      # 排课信息
      schedule_category: "排课类别"
      substitute_flag: "代课标志"
      substitute_ip: "代课IP"
      substitute_teacher_id: "代课教师ID"

      # 教师信息
      teacher_id: "教师ID"
      old_teacher_id: "原教师ID"
      is_record_deleted: "记录是否删除"

      # 课表基础信息
      course_basic_id: "课表基础描述ID"

      # 操作信息
      operator: "操作人"
      operation_time: "操作时间"
      create_time: "创建时间"
      update_time: "更新时间"

      # 排课班级
      schedule_class: "排课班级"

      # 录播相关
      recording_reason: "录播原因"
      link_name: "链接名称"
      qr_code_name: "二维码名称"
      qr_code_address: "二维码地址"
      link_type: "链接类型"

      # 实验室相关
      is_special_lab: "是否特殊实验室"
      lab_reason_id: "实验室原因ID"

      # 上课时间
      actual_start_time: "实际开始时间"
      actual_end_time: "实际结束时间"

      # 系统字段
      created_at: "记录创建时间"
      updated_at: "记录更新时间"

    sync_interval: 1800  # 每30分钟同步一次（课程数据变化较频繁）
    enabled: true

  # H3C科研项目表接口
  - name: "research_project"
    description: "科研项目表同步"
    url: "http://***********:33024/1033769420954/api/ky/fdm_kygl_kjxm"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    # 分页参数配置
    params:
      pageSize: "100"  # 每页记录数
      pageNum: "1"     # 起始页码（程序会自动递增）
    table_name: "research_project"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      # 基本标识信息
      wid: "project_id"               # 项目ID
      xmbh: "project_number"          # 项目编号
      xmmc: "project_name"            # 项目名称
      xmpzh: "project_approval_number" # 项目批准号

      # 项目分类信息
      kydlm: "research_category_code" # 科研大类码
      kydlm_mc: "research_category_name" # 科研大类名称
      xmflm: "project_type_code"      # 项目分类码
      xmflm_mc: "project_type_name"   # 项目分类名称
      xmjbm: "project_level_code"     # 项目级别码
      xmjbm_mc: "project_level_name"  # 项目级别名称
      xmlbm: "project_sub_category_code" # 项目类别码
      xmlbm_mc: "project_sub_category_name" # 项目类别名称

      # 项目状态和描述
      xmzt: "project_status"          # 项目状态
      xmjj: "project_description"     # 项目简介
      ztc: "total_funding"            # 总投资

      # 时间信息
      lxrq_jybbgxky0101023: "approval_date" # 立项日期
      ksrq_jybbgxky0101004: "start_date"    # 开始日期
      jxrq: "end_date"                      # 结束日期
      jhksrq: "planned_start_date"          # 计划开始日期
      jhwcrq: "planned_end_date"            # 计划完成日期

      # 项目负责人信息
      xmfzrh: "project_leader_code"   # 项目负责人号
      xmfzrh_mc: "project_leader_name" # 项目负责人名称
      xmfzrszdwm: "leader_dept_code"  # 项目负责人所在单位码
      xmfzrszdwmc: "leader_dept_name" # 项目负责人所在单位名称
      fzrdh: "leader_phone"           # 负责人电话
      fzryx: "leader_email"           # 负责人邮箱

      # 项目联系人信息
      xmlxrzgh: "contact_job_number"  # 项目联系人职工号
      xmlxrxm: "contact_name"         # 项目联系人姓名
      xmlxrdh: "contact_phone"        # 项目联系人电话
      xmlxryx: "contact_email"        # 项目联系人邮箱

      # 委托单位信息
      xmwtdw: "entrusting_unit"       # 项目委托单位
      gsdwm: "company_unit_code"      # 公司单位码
      gsdwm_mc: "company_unit_name"   # 公司单位名称
      gsjdptm: "company_platform_code" # 公司阶段平台码
      gsjdptm_mc: "company_platform_name" # 公司阶段平台名称

      # 学科分类信息
      yjlbm: "research_type_code"     # 研究类别码
      yjlbm_mc: "research_type_name"  # 研究类别名称
      yjxkm: "research_discipline_code" # 研究学科码
      yjxkm_mc: "research_discipline_name" # 研究学科名称
      ejxkm: "secondary_discipline_code" # 二级学科码
      ejxkm_mc: "secondary_discipline_name" # 二级学科名称
      xmejlbm: "project_secondary_category_code" # 项目二级类别码
      xmejlbm_mc: "project_secondary_category_name" # 项目二级类别名称

      # 经费信息
      htjf: "contract_funding"        # 合同经费
      ptjf: "platform_funding"        # 平台经费
      rjjf: "software_funding"        # 软件经费
      sbjf: "equipment_funding"       # 设备经费
      zcjf: "asset_funding"           # 资产经费

      # 项目执行信息
      xmzxztm: "execution_status_code" # 项目执行状态码
      xmzxztm_mc: "execution_status_name" # 项目执行状态名称
      jhwcqkm: "planned_completion_code" # 计划完成情况码
      jhwcqkm_mc: "planned_completion_name" # 计划完成情况名称
      yqyjcgjxs: "expected_research_results" # 预期研究成果形式

      # 合作信息
      hzxsm: "cooperation_form_code"  # 合作形式码
      hzxsm_mc: "cooperation_form_name" # 合作形式名称
      hzgjdqm: "cooperation_country_code" # 合作国家地区码
      hzgjdqm_mc: "cooperation_country_name" # 合作国家地区名称

      # 其他分类信息
      xmlym: "project_source_code"    # 项目来源码
      xmlym_mc: "project_source_name" # 项目来源名称
      cyfsm: "participation_form_code" # 参与方式码
      cyfsm_mc: "participation_form_name" # 参与方式名称
      wtdwgjm: "entrusting_country_code" # 委托单位国家码
      wtdwgjm_mc: "entrusting_country_name" # 委托单位国家名称
      wtdwszdqm: "entrusting_region_code" # 委托单位所在地区码
      wtdwszdqm_mc: "entrusting_region_name" # 委托单位所在地区名称

      # 社会经济信息
      mjm: "subject_code"             # 门类码
      mjm_mc: "subject_name"          # 门类名称
      shjjxym: "social_economic_code" # 社会经济效益码
      shjjxym_mc: "social_economic_name" # 社会经济效益名称
      xklym: "discipline_source_code" # 学科来源码
      xklym_mc: "discipline_source_name" # 学科来源名称
      ssjsly: "technology_source"     # 所属技术领域
      fwxym: "service_field_code"     # 服务领域码
      fwxym_mc: "service_field_name"  # 服务领域名称
      zzxsm: "organization_form_code" # 组织形式码
      zzxsm_mc: "organization_form_name" # 组织形式名称

      # 统计信息
      xmlym_tj: "project_source_stat_code" # 项目来源统计码
      xmlym_mc_tj: "project_source_stat_name" # 项目来源统计名称
      shjjmbm: "social_economic_target_code" # 社会经济目标码
      shjjmbm_mc: "social_economic_target_name" # 社会经济目标名称
      sshym: "social_science_code"    # 社会科学码
      sshym_mc: "social_science_name" # 社会科学名称
      xkmlkjm: "discipline_category_code" # 学科门类科技码
      xkmlkjm_mc: "discipline_category_name" # 学科门类科技名称

      # 活动和执行信息
      hdlxm: "activity_type_code"     # 活动类型码
      hdlxm_mc: "activity_type_name"  # 活动类型名称
      xmjtxsm: "project_stage_code"   # 项目阶段形式码
      xmjtxsm_mc: "project_stage_name" # 项目阶段形式名称
      sszkt: "research_team"          # 所属组课题

      # 合同和排名信息
      htbh: "contract_number"         # 合同编号
      xxpm: "school_ranking"          # 学校排名
      cyrs: "participants_count"      # 参与人数
      cymd: "participation_density"   # 参与密度

      # 操作和管理信息
      bz: "remarks"                   # 备注
      clrq: "processing_date"         # 处理日期
      czlx: "operation_type"          # 操作类型
      sjly: "data_source"             # 数据来源
      sbrq: "submission_date"         # 申报日期
      dwjsm: "unit_level_code"        # 单位级别码
      dwjsm_mc: "unit_level_name"     # 单位级别名称
      sbxmh: "submission_project_number" # 申报项目号
      xmzy: "project_summary"         # 项目摘要
      xdwh: "coordination_unit_number" # 协调单位号
      ktrq: "start_topic_date"        # 开题日期
      shjjmb: "social_economic_target" # 社会经济目标
      xmlydw: "project_source_unit"   # 项目来源单位
      cyjsrs: "participating_teachers" # 参与教师人数
      cyxsrs: "participating_students" # 参与学生人数
      dwh: "unit_number"              # 单位号
      dwmc: "unit_name"               # 单位名称
      zxbh: "execution_number"        # 执行编号
      zgbm: "competent_department"    # 主管部门

      # 系统字段
      etl_flag: "etl_flag"            # ETL标志
      start_time: "start_time"        # 开始时间
      end_time: "end_time"            # 结束时间
      by1: "reserved_field1"          # 备用字段1
      by2: "reserved_field2"          # 备用字段2

    # 根据实际数据类型定义的字段类型
    field_types:
      # 基本标识信息
      project_id: "VARCHAR(50) PRIMARY KEY"
      project_number: "VARCHAR(50)"
      project_name: "VARCHAR(200) NOT NULL"
      project_approval_number: "VARCHAR(50)"

      # 项目分类信息
      research_category_code: "VARCHAR(10)"
      research_category_name: "VARCHAR(50)"
      project_type_code: "VARCHAR(10)"
      project_type_name: "VARCHAR(50)"
      project_level_code: "VARCHAR(20)"
      project_level_name: "VARCHAR(100)"
      project_sub_category_code: "VARCHAR(20)"
      project_sub_category_name: "VARCHAR(100)"

      # 项目状态和描述
      project_status: "VARCHAR(50)"
      project_description: "TEXT"
      total_funding: "DECIMAL(15,2)"

      # 时间信息
      approval_date: "DATE"
      start_date: "DATE"
      end_date: "DATE"
      planned_start_date: "DATE"
      planned_end_date: "DATE"

      # 项目负责人信息
      project_leader_code: "VARCHAR(50)"
      project_leader_name: "VARCHAR(100)"
      leader_dept_code: "VARCHAR(50)"
      leader_dept_name: "VARCHAR(200)"
      leader_phone: "VARCHAR(20)"
      leader_email: "VARCHAR(100)"

      # 项目联系人信息
      contact_job_number: "VARCHAR(50)"
      contact_name: "VARCHAR(100)"
      contact_phone: "VARCHAR(20)"
      contact_email: "VARCHAR(100)"

      # 委托单位信息
      entrusting_unit: "VARCHAR(200)"
      company_unit_code: "VARCHAR(50)"
      company_unit_name: "VARCHAR(200)"
      company_platform_code: "VARCHAR(50)"
      company_platform_name: "VARCHAR(200)"

      # 学科分类信息
      research_type_code: "VARCHAR(20)"
      research_type_name: "VARCHAR(100)"
      research_discipline_code: "VARCHAR(20)"
      research_discipline_name: "VARCHAR(100)"
      secondary_discipline_code: "VARCHAR(20)"
      secondary_discipline_name: "VARCHAR(100)"
      project_secondary_category_code: "VARCHAR(20)"
      project_secondary_category_name: "VARCHAR(100)"

      # 经费信息
      contract_funding: "DECIMAL(15,2)"
      platform_funding: "DECIMAL(15,2)"
      software_funding: "DECIMAL(15,2)"
      equipment_funding: "DECIMAL(15,2)"
      asset_funding: "DECIMAL(15,2)"

      # 项目执行信息
      execution_status_code: "VARCHAR(20)"
      execution_status_name: "VARCHAR(100)"
      planned_completion_code: "VARCHAR(20)"
      planned_completion_name: "VARCHAR(100)"
      expected_research_results: "TEXT"

      # 合作信息
      cooperation_form_code: "VARCHAR(20)"
      cooperation_form_name: "VARCHAR(100)"
      cooperation_country_code: "VARCHAR(20)"
      cooperation_country_name: "VARCHAR(100)"

      # 其他分类信息
      project_source_code: "VARCHAR(20)"
      project_source_name: "VARCHAR(100)"
      participation_form_code: "VARCHAR(20)"
      participation_form_name: "VARCHAR(100)"
      entrusting_country_code: "VARCHAR(20)"
      entrusting_country_name: "VARCHAR(100)"
      entrusting_region_code: "VARCHAR(20)"
      entrusting_region_name: "VARCHAR(100)"

      # 社会经济信息
      subject_code: "VARCHAR(20)"
      subject_name: "VARCHAR(100)"
      social_economic_code: "VARCHAR(20)"
      social_economic_name: "VARCHAR(100)"
      discipline_source_code: "VARCHAR(20)"
      discipline_source_name: "VARCHAR(100)"
      technology_source: "VARCHAR(200)"
      service_field_code: "VARCHAR(20)"
      service_field_name: "VARCHAR(100)"
      organization_form_code: "VARCHAR(20)"
      organization_form_name: "VARCHAR(100)"

      # 统计信息
      project_source_stat_code: "VARCHAR(20)"
      project_source_stat_name: "VARCHAR(100)"
      social_economic_target_code: "VARCHAR(20)"
      social_economic_target_name: "VARCHAR(100)"
      social_science_code: "VARCHAR(20)"
      social_science_name: "VARCHAR(100)"
      discipline_category_code: "VARCHAR(20)"
      discipline_category_name: "VARCHAR(100)"

      # 活动和执行信息
      activity_type_code: "VARCHAR(20)"
      activity_type_name: "VARCHAR(100)"
      project_stage_code: "VARCHAR(20)"
      project_stage_name: "VARCHAR(100)"
      research_team: "VARCHAR(200)"

      # 合同和排名信息
      contract_number: "VARCHAR(50)"
      school_ranking: "VARCHAR(20)"
      participants_count: "INT"
      participation_density: "VARCHAR(50)"

      # 操作和管理信息
      remarks: "TEXT"
      processing_date: "DATE"
      operation_type: "VARCHAR(50)"
      data_source: "VARCHAR(100)"
      submission_date: "DATE"
      unit_level_code: "VARCHAR(20)"
      unit_level_name: "VARCHAR(100)"
      submission_project_number: "VARCHAR(50)"
      project_summary: "TEXT"
      coordination_unit_number: "VARCHAR(50)"
      start_topic_date: "DATE"
      social_economic_target: "VARCHAR(200)"
      project_source_unit: "VARCHAR(200)"
      participating_teachers: "INT"
      participating_students: "INT"
      unit_number: "VARCHAR(50)"
      unit_name: "VARCHAR(200)"
      execution_number: "VARCHAR(50)"
      competent_department: "VARCHAR(100)"

      # 系统字段
      etl_flag: "VARCHAR(20)"
      start_time: "DATETIME"
      end_time: "DATETIME"
      reserved_field1: "VARCHAR(200)"
      reserved_field2: "VARCHAR(200)"
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"

    # 字段注释说明
    field_comments:
      # 基本标识信息
      project_id: "项目ID"
      project_number: "项目编号"
      project_name: "项目名称"
      project_approval_number: "项目批准号"

      # 项目分类信息
      research_category_code: "科研大类码"
      research_category_name: "科研大类名称"
      project_type_code: "项目分类码"
      project_type_name: "项目分类名称"
      project_level_code: "项目级别码"
      project_level_name: "项目级别名称"
      project_sub_category_code: "项目类别码"
      project_sub_category_name: "项目类别名称"

      # 项目状态和描述
      project_status: "项目状态"
      project_description: "项目简介"
      total_funding: "总投资"

      # 时间信息
      approval_date: "立项日期"
      start_date: "开始日期"
      end_date: "结束日期"
      planned_start_date: "计划开始日期"
      planned_end_date: "计划完成日期"

      # 项目负责人信息
      project_leader_code: "项目负责人号"
      project_leader_name: "项目负责人名称"
      leader_dept_code: "项目负责人所在单位码"
      leader_dept_name: "项目负责人所在单位名称"
      leader_phone: "负责人电话"
      leader_email: "负责人邮箱"

      # 项目联系人信息
      contact_job_number: "项目联系人职工号"
      contact_name: "项目联系人姓名"
      contact_phone: "项目联系人电话"
      contact_email: "项目联系人邮箱"

      # 委托单位信息
      entrusting_unit: "项目委托单位"
      company_unit_code: "公司单位码"
      company_unit_name: "公司单位名称"
      company_platform_code: "公司阶段平台码"
      company_platform_name: "公司阶段平台名称"

      # 学科分类信息
      research_type_code: "研究类别码"
      research_type_name: "研究类别名称"
      research_discipline_code: "研究学科码"
      research_discipline_name: "研究学科名称"
      secondary_discipline_code: "二级学科码"
      secondary_discipline_name: "二级学科名称"
      project_secondary_category_code: "项目二级类别码"
      project_secondary_category_name: "项目二级类别名称"

      # 经费信息
      contract_funding: "合同经费"
      platform_funding: "平台经费"
      software_funding: "软件经费"
      equipment_funding: "设备经费"
      asset_funding: "资产经费"

      # 项目执行信息
      execution_status_code: "项目执行状态码"
      execution_status_name: "项目执行状态名称"
      planned_completion_code: "计划完成情况码"
      planned_completion_name: "计划完成情况名称"
      expected_research_results: "预期研究成果形式"

      # 合作信息
      cooperation_form_code: "合作形式码"
      cooperation_form_name: "合作形式名称"
      cooperation_country_code: "合作国家地区码"
      cooperation_country_name: "合作国家地区名称"

      # 其他分类信息
      project_source_code: "项目来源码"
      project_source_name: "项目来源名称"
      participation_form_code: "参与方式码"
      participation_form_name: "参与方式名称"
      entrusting_country_code: "委托单位国家码"
      entrusting_country_name: "委托单位国家名称"
      entrusting_region_code: "委托单位所在地区码"
      entrusting_region_name: "委托单位所在地区名称"

      # 社会经济信息
      subject_code: "门类码"
      subject_name: "门类名称"
      social_economic_code: "社会经济效益码"
      social_economic_name: "社会经济效益名称"
      discipline_source_code: "学科来源码"
      discipline_source_name: "学科来源名称"
      technology_source: "所属技术领域"
      service_field_code: "服务领域码"
      service_field_name: "服务领域名称"
      organization_form_code: "组织形式码"
      organization_form_name: "组织形式名称"

      # 统计信息
      project_source_stat_code: "项目来源统计码"
      project_source_stat_name: "项目来源统计名称"
      social_economic_target_code: "社会经济目标码"
      social_economic_target_name: "社会经济目标名称"
      social_science_code: "社会科学码"
      social_science_name: "社会科学名称"
      discipline_category_code: "学科门类科技码"
      discipline_category_name: "学科门类科技名称"

      # 活动和执行信息
      activity_type_code: "活动类型码"
      activity_type_name: "活动类型名称"
      project_stage_code: "项目阶段形式码"
      project_stage_name: "项目阶段形式名称"
      research_team: "所属组课题"

      # 合同和排名信息
      contract_number: "合同编号"
      school_ranking: "学校排名"
      participants_count: "参与人数"
      participation_density: "参与密度"

      # 操作和管理信息
      remarks: "备注"
      processing_date: "处理日期"
      operation_type: "操作类型"
      data_source: "数据来源"
      submission_date: "申报日期"
      unit_level_code: "单位级别码"
      unit_level_name: "单位级别名称"
      submission_project_number: "申报项目号"
      project_summary: "项目摘要"
      coordination_unit_number: "协调单位号"
      start_topic_date: "开题日期"
      social_economic_target: "社会经济目标"
      project_source_unit: "项目来源单位"
      participating_teachers: "参与教师人数"
      participating_students: "参与学生人数"
      unit_number: "单位号"
      unit_name: "单位名称"
      execution_number: "执行编号"
      competent_department: "主管部门"

      # 系统字段
      etl_flag: "ETL标志"
      start_time: "开始时间"
      end_time: "结束时间"
      reserved_field1: "备用字段1"
      reserved_field2: "备用字段2"
      created_at: "记录创建时间"
      updated_at: "记录更新时间"

    sync_interval: 3600  # 每小时同步一次
    enabled: true


# 全局配置
global:
  # 日志级别: debug, info, warn, error
  log_level: "info"
  # 日志文件路径
  log_file: "data_sync.log"
  # 同步模式: replace（替换）, append（追加）, upsert（更新插入）
  sync_mode: "upsert"  # 推荐使用upsert模式
  # 错误重试次数
  retry_count: 5
  # 请求超时时间（秒）
  request_timeout: 60
