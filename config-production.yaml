# 生产环境API到MySQL数据同步配置文件

# MySQL数据库配置
database:
  host: "localhost"
  port: 3306
  username: "root"
  password: "Bigbang!"
  database: "data_sync"
  charset: "utf8mb4"
  max_open_conns: 20
  max_idle_conns: 10
  conn_max_lifetime: 3600  # 1小时

# API接口配置列表
apis:
  # H3C学生基本数据接口
  - name: "student_basic"
    description: "学生基本数据子类表同步"
    url: "https://mock.fangcloud.net/1033769420954/api/v1/xs/fdm/fdm_gxxs_xsjbsj"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    # 分页参数配置
    params:
      pageSize: "100"  # 每页记录数
      pageNum: "1"     # 起始页码（程序会自动递增）
    table_name: "student_basic"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      xh: "student_number"        # 学号
      xm: "student_name"          # 姓名
      ywxm: "english_name"        # 英文姓名
      xmpy: "name_pinyin"         # 姓名拼音
      yxdm: "college_code"        # 学院代码
      yxmc: "college_name"        # 学院名称
      zydm: "major_code"          # 专业代码
      zymc: "major_name"          # 专业名称
      bjdm: "class_code"          # 班级代码
      bjmc: "class_name"          # 班级名称
      xsdqztm: "student_status_code"  # 学生当前状态码
      xsdqzt: "student_status"    # 学生当前状态
      dzxx: "email"               # 电子邮箱
      xjh: "student_id_number"    # 学籍号
    # 根据实际数据类型定义的字段类型
    field_types:
      student_number: "VARCHAR(20) PRIMARY KEY"
      student_name: "VARCHAR(50) NOT NULL"
      english_name: "VARCHAR(100)"
      name_pinyin: "VARCHAR(100)"
      college_code: "VARCHAR(20)"
      college_name: "VARCHAR(100)"
      major_code: "VARCHAR(20)"
      major_name: "VARCHAR(100)"
      class_code: "VARCHAR(20)"
      class_name: "VARCHAR(100)"
      student_status_code: "VARCHAR(10)"
      student_status: "VARCHAR(20)"
      email: "VARCHAR(100)"
      student_id_number: "VARCHAR(30)"
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    # 字段注释说明
    field_comments:
      student_number: "学号"
      student_name: "学生姓名"
      english_name: "英文姓名"
      name_pinyin: "姓名拼音"
      college_code: "学院代码"
      college_name: "学院名称"
      major_code: "专业代码"
      major_name: "专业名称"
      class_code: "班级代码"
      class_name: "班级名称"
      student_status_code: "学生当前状态码"
      student_status: "学生当前状态"
      email: "电子邮箱"
      student_id_number: "学籍号"
      created_at: "记录创建时间"
      updated_at: "记录更新时间"
    sync_interval: 3600  # 每小时同步一次
    enabled: true

  # H3C院系所单位基本信息接口
  - name: "department_basic"
    description: "院系所单位基本信息同步"
    url: "https://mock.fangcloud.net/1033769420954/data_center/xx/v1/fdm_xx_dw"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    # 分页参数配置
    params:
      pageSize: "100"  # 每页记录数
      pageNum: "1"     # 起始页码（程序会自动递增）
    table_name: "department_basic"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      dwh: "department_code"          # 单位号
      dwmc: "department_name"         # 单位名称
      dwywmc: "department_english_name"  # 单位英文名称
      dwjc: "department_short_name"   # 单位简称
      lsdwh: "parent_department_code" # 隶属单位号
      lsdwh_mc: "parent_department_name"  # 隶属单位名称
      dwfzrh: "department_head_code"  # 单位负责人号
      dwfzrh_mc: "department_head_name"   # 单位负责人名称
      sxrq: "effective_date"          # 生效日期
      dwyxbs: "department_validity_flag"  # 单位有效标识
    # 根据实际数据类型定义的字段类型
    field_types:
      department_code: "VARCHAR(20) PRIMARY KEY"
      department_name: "VARCHAR(200) NOT NULL"
      department_english_name: "VARCHAR(200)"
      department_short_name: "VARCHAR(100)"
      parent_department_code: "VARCHAR(20)"
      parent_department_name: "VARCHAR(200)"
      department_head_code: "VARCHAR(20)"
      department_head_name: "VARCHAR(100)"
      effective_date: "VARCHAR(20)"
      department_validity_flag: "VARCHAR(10)"
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    # 字段注释说明
    field_comments:
      department_code: "单位号"
      department_name: "单位名称"
      department_english_name: "单位英文名称"
      department_short_name: "单位简称"
      parent_department_code: "隶属单位号"
      parent_department_name: "隶属单位名称"
      department_head_code: "单位负责人号"
      department_head_name: "单位负责人名称"
      effective_date: "生效日期"
      department_validity_flag: "单位有效标识"
      created_at: "记录创建时间"
      updated_at: "记录更新时间"
    sync_interval: 7200  # 每2小时同步一次（部门信息变化较少）
    enabled: true

  # 员工基本数据接口
  - name: "teacher_basic"
    description: "员工基本数据同步"
    url: "https://mock.fangcloud.net/1033769420954/data_center/jg/v1/fdm_jg_jbxx_lx"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    table_name: "teacher_basic"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      gh: "employee_number"        # 工号
      xm: "employee_name"          # 姓名
      dwbm: "department_code"      # 部门编码
      dwmc: "department_name"      # 部门名称
      sfzb: "position_level"       # 是否主管
      jzglb: "job_category"        # 岗位类别
      jzgdqzt: "employment_status" # 岗位当前状态
      xb: "gender"                 # 性别
    # 根据实际数据类型定义的字段类型
    field_types:
      employee_number: "VARCHAR(20) PRIMARY KEY"
      employee_name: "VARCHAR(50) NOT NULL"
      department_code: "VARCHAR(20)"
      department_name: "VARCHAR(100)"
      position_level: "VARCHAR(20)"
      job_category: "VARCHAR(50)"
      employment_status: "VARCHAR(20)"
      gender: "VARCHAR(10)"
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    # 字段注释说明
    field_comments:
      employee_number: "员工工号"
      employee_name: "员工姓名"
      department_code: "部门编码"
      department_name: "部门名称"
      position_level: "职位级别/是否主管"
      job_category: "岗位类别"
      employment_status: "岗位当前状态"
      gender: "性别"
      created_at: "记录创建时间"
      updated_at: "记录更新时间"
    sync_interval: 3600  # 每小时同步一次
    enabled: true

  
# 全局配置
global:
  # 日志级别: debug, info, warn, error
  log_level: "info"
  # 日志文件路径
  log_file: "data_sync.log"
  # 同步模式: replace（替换）, append（追加）, upsert（更新插入）
  sync_mode: "upsert"  # 推荐使用upsert模式
  # 错误重试次数
  retry_count: 5
  # 请求超时时间（秒）
  request_timeout: 60
