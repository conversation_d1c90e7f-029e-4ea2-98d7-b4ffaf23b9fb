# 生产环境API到MySQL数据同步配置文件

# MySQL数据库配置
database:
  host: "localhost"
  port: 3306
  username: "root"
  password: "Bigbang!"
  database: "data_sync"
  charset: "utf8mb4"
  max_open_conns: 20
  max_idle_conns: 10
  conn_max_lifetime: 3600  # 1小时

# API接口配置列表
apis:
  # H3C学生基本数据接口
  - name: "student_basic"
    description: "学生基本数据子类表同步"
    url: "https://mock.fangcloud.net/1033769420954/api/v1/xs/fdm/fdm_gxxs_xsjbsj"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    # 分页参数配置
    params:
      pageSize: "100"  # 每页记录数
      pageNum: "1"     # 起始页码（程序会自动递增）
    table_name: "student_basic"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      # 基本信息
      xh: "student_number"        # 学号
      xm: "student_name"          # 姓名
      ywxm: "english_name"        # 英文姓名
      xmpy: "name_pinyin"         # 姓名拼音
      xjh: "student_id_number"    # 学籍号

      # 学院专业班级信息
      yxdm: "college_code"        # 学院代码
      yxmc: "college_name"        # 学院名称
      zydm: "major_code"          # 专业代码
      zymc: "major_name"          # 专业名称
      bjdm: "class_code"          # 班级代码
      bjmc: "class_name"          # 班级名称

      # 学生状态信息
      xsdqztm: "student_status_code"  # 学生当前状态码
      xsdqzt: "student_status"    # 学生当前状态
      rxztm: "enrollment_status_code"  # 入学状态码
      rxzt: "enrollment_status"   # 入学状态

      # 个人基本信息
      xbm: "gender_code"          # 性别码
      xb: "gender"                # 性别
      csrq: "birth_date"          # 出生日期
      mzm: "ethnicity_code"       # 民族码
      mz: "ethnicity"             # 民族
      sfzjlxm: "id_type_code"     # 身份证件类型码
      sfzjlx: "id_type"           # 身份证件类型
      sfzh: "id_number"           # 身份证号

      # 学历培养信息
      pyccm: "education_level_code"  # 培养层次码
      pycc: "education_level"     # 培养层次
      rxnf: "enrollment_year"     # 入学年份
      xznj: "current_grade"       # 现在年级

      # 政治面貌和健康状况
      zzmmm: "political_status_code"  # 政治面貌码
      zzmm: "political_status"    # 政治面貌
      jkzkm: "health_status_code" # 健康状况码
      jkzk: "health_status"       # 健康状况

      # 联系方式
      dzxx: "email"               # 电子邮箱
      txdz: "contact_address"     # 通讯地址
      yzbm: "postal_code"         # 邮政编码
      jstxh: "mobile_phone"       # 手机号
      qqh: "qq_number"            # QQ号
      wxh: "wechat_number"        # 微信号

      # 家庭信息
      jtzz: "family_address"      # 家庭住址
      fqxm: "father_name"         # 父亲姓名
      fqzw: "father_occupation"   # 父亲职务
      mqxm: "mother_name"         # 母亲姓名
      mqzw: "mother_occupation"   # 母亲职务

      # 录取信息
      rxcj: "admission_score"     # 入学成绩
      lqpcm: "admission_batch_code"  # 录取批次码
      lqpc: "admission_batch"     # 录取批次
      lqlbm: "admission_category_code"  # 录取类别码
      lqlb: "admission_category"  # 录取类别

      # 其他标识
      sfdszn: "is_single_child"   # 是否独生子女
      sfsc: "is_first_generation" # 是否首次
      cym: "source_language_code" # 从语言码
      sfzczxkt: "is_in_system"    # 是否在册在校开通
    # 根据实际数据类型定义的字段类型
    field_types:
      # 基本信息
      student_number: "VARCHAR(20) PRIMARY KEY"
      student_name: "VARCHAR(50) NOT NULL"
      english_name: "VARCHAR(100)"
      name_pinyin: "VARCHAR(100)"
      student_id_number: "VARCHAR(30)"

      # 学院专业班级信息
      college_code: "VARCHAR(20)"
      college_name: "VARCHAR(100)"
      major_code: "VARCHAR(20)"
      major_name: "VARCHAR(100)"
      class_code: "VARCHAR(20)"
      class_name: "VARCHAR(100)"

      # 学生状态信息
      student_status_code: "VARCHAR(10)"
      student_status: "VARCHAR(20)"
      enrollment_status_code: "VARCHAR(10)"
      enrollment_status: "VARCHAR(20)"

      # 个人基本信息
      gender_code: "VARCHAR(5)"
      gender: "VARCHAR(10)"
      birth_date: "VARCHAR(20)"
      ethnicity_code: "VARCHAR(10)"
      ethnicity: "VARCHAR(20)"
      id_type_code: "VARCHAR(10)"
      id_type: "VARCHAR(30)"
      id_number: "VARCHAR(30)"

      # 学历培养信息
      education_level_code: "VARCHAR(10)"
      education_level: "VARCHAR(30)"
      enrollment_year: "VARCHAR(10)"
      current_grade: "VARCHAR(10)"

      # 政治面貌和健康状况
      political_status_code: "VARCHAR(10)"
      political_status: "VARCHAR(30)"
      health_status_code: "VARCHAR(10)"
      health_status: "VARCHAR(30)"

      # 联系方式
      email: "VARCHAR(100)"
      contact_address: "VARCHAR(200)"
      postal_code: "VARCHAR(20)"
      mobile_phone: "VARCHAR(20)"
      qq_number: "VARCHAR(20)"
      wechat_number: "VARCHAR(50)"

      # 家庭信息
      family_address: "VARCHAR(200)"
      father_name: "VARCHAR(50)"
      father_occupation: "VARCHAR(100)"
      mother_name: "VARCHAR(50)"
      mother_occupation: "VARCHAR(100)"

      # 录取信息
      admission_score: "VARCHAR(20)"
      admission_batch_code: "VARCHAR(10)"
      admission_batch: "VARCHAR(50)"
      admission_category_code: "VARCHAR(10)"
      admission_category: "VARCHAR(50)"

      # 其他标识
      is_single_child: "VARCHAR(5)"
      is_first_generation: "VARCHAR(5)"
      source_language_code: "VARCHAR(10)"
      is_in_system: "VARCHAR(5)"

      # 系统字段
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    # 字段注释说明
    field_comments:
      # 基本信息
      student_number: "学号"
      student_name: "学生姓名"
      english_name: "英文姓名"
      name_pinyin: "姓名拼音"
      student_id_number: "学籍号"

      # 学院专业班级信息
      college_code: "学院代码"
      college_name: "学院名称"
      major_code: "专业代码"
      major_name: "专业名称"
      class_code: "班级代码"
      class_name: "班级名称"

      # 学生状态信息
      student_status_code: "学生当前状态码"
      student_status: "学生当前状态"
      enrollment_status_code: "入学状态码"
      enrollment_status: "入学状态"

      # 个人基本信息
      gender_code: "性别码"
      gender: "性别"
      birth_date: "出生日期"
      ethnicity_code: "民族码"
      ethnicity: "民族"
      id_type_code: "身份证件类型码"
      id_type: "身份证件类型"
      id_number: "身份证号"

      # 学历培养信息
      education_level_code: "培养层次码"
      education_level: "培养层次"
      enrollment_year: "入学年份"
      current_grade: "现在年级"

      # 政治面貌和健康状况
      political_status_code: "政治面貌码"
      political_status: "政治面貌"
      health_status_code: "健康状况码"
      health_status: "健康状况"

      # 联系方式
      email: "电子邮箱"
      contact_address: "通讯地址"
      postal_code: "邮政编码"
      mobile_phone: "手机号"
      qq_number: "QQ号"
      wechat_number: "微信号"

      # 家庭信息
      family_address: "家庭住址"
      father_name: "父亲姓名"
      father_occupation: "父亲职务"
      mother_name: "母亲姓名"
      mother_occupation: "母亲职务"

      # 录取信息
      admission_score: "入学成绩"
      admission_batch_code: "录取批次码"
      admission_batch: "录取批次"
      admission_category_code: "录取类别码"
      admission_category: "录取类别"

      # 其他标识
      is_single_child: "是否独生子女"
      is_first_generation: "是否首次"
      source_language_code: "从语言码"
      is_in_system: "是否在册在校开通"

      # 系统字段
      created_at: "记录创建时间"
      updated_at: "记录更新时间"
    sync_interval: 3600  # 每小时同步一次
    enabled: true

  # H3C院系所单位基本信息接口
  - name: "department_basic"
    description: "院系所单位基本信息同步"
    url: "https://mock.fangcloud.net/1033769420954/data_center/xx/v1/fdm_xx_dw"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    # 分页参数配置
    params:
      pageSize: "100"  # 每页记录数
      pageNum: "1"     # 起始页码（程序会自动递增）
    table_name: "department_basic"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      dwh: "department_code"          # 单位号
      dwmc: "department_name"         # 单位名称
      dwywmc: "department_english_name"  # 单位英文名称
      dwjc: "department_short_name"   # 单位简称
      lsdwh: "parent_department_code" # 隶属单位号
      lsdwh_mc: "parent_department_name"  # 隶属单位名称
      dwfzrh: "department_head_code"  # 单位负责人号
      dwfzrh_mc: "department_head_name"   # 单位负责人名称
      sxrq: "effective_date"          # 生效日期
      dwyxbs: "department_validity_flag"  # 单位有效标识
    # 根据实际数据类型定义的字段类型
    field_types:
      department_code: "VARCHAR(20) PRIMARY KEY"
      department_name: "VARCHAR(200) NOT NULL"
      department_english_name: "VARCHAR(200)"
      department_short_name: "VARCHAR(100)"
      parent_department_code: "VARCHAR(20)"
      parent_department_name: "VARCHAR(200)"
      department_head_code: "VARCHAR(20)"
      department_head_name: "VARCHAR(100)"
      effective_date: "VARCHAR(20)"
      department_validity_flag: "VARCHAR(10)"
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    # 字段注释说明
    field_comments:
      department_code: "单位号"
      department_name: "单位名称"
      department_english_name: "单位英文名称"
      department_short_name: "单位简称"
      parent_department_code: "隶属单位号"
      parent_department_name: "隶属单位名称"
      department_head_code: "单位负责人号"
      department_head_name: "单位负责人名称"
      effective_date: "生效日期"
      department_validity_flag: "单位有效标识"
      created_at: "记录创建时间"
      updated_at: "记录更新时间"
    sync_interval: 7200  # 每2小时同步一次（部门信息变化较少）
    enabled: true

  # 员工基本数据接口
  - name: "teacher_basic"
    description: "员工基本数据同步"
    url: "https://mock.fangcloud.net/1033769420954/data_center/jg/v1/fdm_jg_jbxx_lx"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    # 分页参数配置
    params:
      pageSize: "100"  # 每页记录数
      pageNum: "1"     # 起始页码（程序会自动递增）
    table_name: "teacher_basic"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      # 基本信息
      gh: "teacher_number"        # 工号
      xm: "teacher_name"          # 姓名
      zjhm: "id_number"           # 证件号码
      xbm: "gender_code"          # 性别码
      xb: "gender"                # 性别
      xbmc: "gender_name"         # 性别名称
      csrq: "birth_date"          # 出生日期

      # 国籍民族信息
      gjm: "country_code"         # 国家码
      gj: "country"               # 国家
      gjmc: "country_name"        # 国家名称
      mzm: "ethnicity_code"       # 民族码
      mz: "ethnicity"             # 民族
      mzmc: "ethnicity_name"      # 民族名称
      jgm: "origin_code"          # 籍贯码
      jg: "origin"                # 籍贯

      # 证件信息
      zjlxm: "id_type_code"       # 证件类型码
      zjlxmc: "id_type_name"      # 证件类型名称

      # 政治面貌
      zzmmm: "political_status_code"    # 政治面貌码
      zzmmmc: "political_status_name"   # 政治面貌名称
      hyzkm: "marital_status_code"      # 婚姻状况码
      hyzkmc: "marital_status_name"     # 婚姻状况名称

      # 工作信息
      prsj: "appointment_date"    # 聘任时间
      rzrq: "entry_date"          # 入职日期
      dwbm: "department_code"     # 单位编码
      dwmc: "department_name"     # 单位名称
      gwlb: "position_category"   # 岗位类别
      ydlx: "mobility_type"       # 异动类型
      sfzb: "is_on_duty"          # 是否在编
      sfssxjs: "is_part_time_teacher"  # 是否双师型教师
      xzzw: "administrative_position"  # 行政职务
      zyjszw: "professional_title_position"  # 专业技术职务
      zc: "professional_title"    # 职称

      # 学历学位信息
      zgxl: "highest_education"   # 最高学历
      zgxw: "highest_degree"      # 最高学位
      byyx: "graduate_school"     # 毕业院校
      sxzy: "major"               # 所学专业

      # 教职工状态
      jzglbm: "staff_category_code"     # 教职工类别码
      jzglbmc: "staff_category_name"    # 教职工类别名称
      jzgdqztm: "current_status_code"   # 教职工当前状态码
      jzgdqztmc: "current_status_name"  # 教职工当前状态名称
      jzgdqzt: "current_status"         # 教职工当前状态
      jzglb: "staff_category"           # 教职工类别
      jzgly: "staff_source"             # 教职工来源

      # 其他信息
      qdhtqk: "contract_situation"      # 签订合同情况
      lxdh: "contact_phone"             # 联系电话
      dzyx: "email"                     # 电子邮箱
      zp: "photo"                       # 照片
      sklx: "subject_category"          # 学科类型
      sfwbkssk: "is_external_lecturer"  # 是否外聘课时授课
      xxjylx: "continuing_education_type"  # 继续教育类型
      bxnsfsk: "is_part_time_teaching"  # 本校内是否授课
      bskyy: "no_teaching_reason"       # 不授课原因
      jxly: "teaching_experience"       # 教学履历
      sffzsszdk: "is_vocational_skills"  # 是否职业技能
    # 根据实际数据类型定义的字段类型
    field_types:
      # 基本信息
      teacher_number: "VARCHAR(20) PRIMARY KEY"
      teacher_name: "VARCHAR(50) NOT NULL"
      id_number: "VARCHAR(30)"
      gender_code: "VARCHAR(5)"
      gender: "VARCHAR(10)"
      gender_name: "VARCHAR(10)"
      birth_date: "DATE"

      # 国籍民族信息
      country_code: "VARCHAR(10)"
      country: "VARCHAR(50)"
      country_name: "VARCHAR(50)"
      ethnicity_code: "VARCHAR(10)"
      ethnicity: "VARCHAR(20)"
      ethnicity_name: "VARCHAR(20)"
      origin_code: "VARCHAR(10)"
      origin: "VARCHAR(50)"

      # 证件信息
      id_type_code: "VARCHAR(10)"
      id_type_name: "VARCHAR(30)"

      # 政治面貌
      political_status_code: "VARCHAR(10)"
      political_status_name: "VARCHAR(30)"
      marital_status_code: "VARCHAR(10)"
      marital_status_name: "VARCHAR(20)"

      # 工作信息
      appointment_date: "DATE"
      entry_date: "DATE"
      department_code: "VARCHAR(20)"
      department_name: "VARCHAR(100)"
      position_category: "VARCHAR(50)"
      mobility_type: "VARCHAR(30)"
      is_on_duty: "VARCHAR(5)"
      is_part_time_teacher: "VARCHAR(5)"
      administrative_position: "VARCHAR(50)"
      professional_title_position: "VARCHAR(50)"
      professional_title: "VARCHAR(50)"

      # 学历学位信息
      highest_education: "VARCHAR(30)"
      highest_degree: "VARCHAR(30)"
      graduate_school: "VARCHAR(100)"
      major: "VARCHAR(100)"

      # 教职工状态
      staff_category_code: "VARCHAR(10)"
      staff_category_name: "VARCHAR(50)"
      current_status_code: "VARCHAR(10)"
      current_status_name: "VARCHAR(30)"
      current_status: "VARCHAR(30)"
      staff_category: "VARCHAR(50)"
      staff_source: "VARCHAR(50)"

      # 其他信息
      contract_situation: "VARCHAR(100)"
      contact_phone: "VARCHAR(20)"
      email: "VARCHAR(100)"
      photo: "TEXT"
      subject_category: "VARCHAR(50)"
      is_external_lecturer: "VARCHAR(5)"
      continuing_education_type: "VARCHAR(50)"
      is_part_time_teaching: "VARCHAR(5)"
      no_teaching_reason: "VARCHAR(100)"
      teaching_experience: "TEXT"
      is_vocational_skills: "VARCHAR(5)"

      # 系统字段
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    # 字段注释说明
    field_comments:
      # 基本信息
      teacher_number: "教师工号"
      teacher_name: "教师姓名"
      id_number: "证件号码"
      gender_code: "性别码"
      gender: "性别"
      gender_name: "性别名称"
      birth_date: "出生日期"

      # 国籍民族信息
      country_code: "国家码"
      country: "国家"
      country_name: "国家名称"
      ethnicity_code: "民族码"
      ethnicity: "民族"
      ethnicity_name: "民族名称"
      origin_code: "籍贯码"
      origin: "籍贯"

      # 证件信息
      id_type_code: "证件类型码"
      id_type_name: "证件类型名称"

      # 政治面貌
      political_status_code: "政治面貌码"
      political_status_name: "政治面貌名称"
      marital_status_code: "婚姻状况码"
      marital_status_name: "婚姻状况名称"

      # 工作信息
      appointment_date: "聘任时间"
      entry_date: "入职日期"
      department_code: "单位编码"
      department_name: "单位名称"
      position_category: "岗位类别"
      mobility_type: "异动类型"
      is_on_duty: "是否在编"
      is_part_time_teacher: "是否双师型教师"
      administrative_position: "行政职务"
      professional_title_position: "专业技术职务"
      professional_title: "职称"

      # 学历学位信息
      highest_education: "最高学历"
      highest_degree: "最高学位"
      graduate_school: "毕业院校"
      major: "所学专业"

      # 教职工状态
      staff_category_code: "教职工类别码"
      staff_category_name: "教职工类别名称"
      current_status_code: "教职工当前状态码"
      current_status_name: "教职工当前状态名称"
      current_status: "教职工当前状态"
      staff_category: "教职工类别"
      staff_source: "教职工来源"

      # 其他信息
      contract_situation: "签订合同情况"
      contact_phone: "联系电话"
      email: "电子邮箱"
      photo: "照片"
      subject_category: "学科类型"
      is_external_lecturer: "是否外聘课时授课"
      continuing_education_type: "继续教育类型"
      is_part_time_teaching: "本校内是否授课"
      no_teaching_reason: "不授课原因"
      teaching_experience: "教学履历"
      is_vocational_skills: "是否职业技能"

      # 系统字段
      created_at: "记录创建时间"
      updated_at: "记录更新时间"
    sync_interval: 3600  # 每小时同步一次
    enabled: true

  # H3C课程数据表接口
  - name: "course_schedule"
    description: "课程数据表同步"
    url: "https://mock.fangcloud.net/api/v1/fdm/gxjx/fdm_gxjx_kbsj"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    # 分页参数配置
    params:
      pageSize: "100"  # 每页记录数
      pageNum: "1"     # 起始页码（程序会自动递增）
    table_name: "course_schedule"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      # 基本标识信息
      apid: "schedule_id"         # 排课ID
      xnxqid: "semester_id"       # 学年学期ID
      xnxq: "semester"            # 学年学期

      # 开课信息
      kkd: "course_location_code" # 开课地点代码
      kkdlb: "course_location_type" # 开课地点类别
      sjbz: "time_flag"           # 时间标志
      kkzc: "course_weeks"        # 开课周次
      kkzcmx: "course_weeks_detail" # 开课周次明细

      # 课程时间信息
      kcsjbh: "course_time_code"  # 课程时间编号
      kcsj: "course_time"         # 课程时间
      kcsjmx: "course_time_detail" # 课程时间明细
      kssj: "start_time"          # 开始时间
      jssj: "end_time"            # 结束时间
      xq: "weekday"               # 星期

      # 排课信息
      pklb: "schedule_category"   # 排课类别
      dkbz: "substitute_flag"     # 代课标志
      dkip: "substitute_ip"       # 代课IP
      dkrjsid: "substitute_teacher_id" # 代课教师ID

      # 教师信息
      jsid: "teacher_id"          # 教师ID
      oldjsid: "old_teacher_id"   # 原教师ID
      jlsfsc: "is_record_deleted" # 记录是否删除

      # 课表基础信息
      kbjcmsid: "course_basic_id" # 课表基础描述ID

      # 操作信息
      czr: "operator"             # 操作人
      czsj: "operation_time"      # 操作时间
      cjsj: "create_time"         # 创建时间
      gxsj: "update_time"         # 更新时间

      # 排课班级
      pkbj: "schedule_class"      # 排课班级

      # 录播相关
      lpyy: "recording_reason"    # 录播原因
      ljmc: "link_name"           # 链接名称
      ewmmc: "qr_code_name"       # 二维码名称
      ewmdz: "qr_code_address"    # 二维码地址
      ljlx: "link_type"           # 链接类型

      # 实验室相关
      sftssy: "is_special_lab"    # 是否特殊实验室
      syyyid: "lab_reason_id"     # 实验室原因ID

      # 上课时间
      skkssj: "actual_start_time" # 实际开始时间
      skjssj: "actual_end_time"   # 实际结束时间

    # 根据实际数据类型定义的字段类型
    field_types:
      # 基本标识信息
      schedule_id: "VARCHAR(50) PRIMARY KEY"
      semester_id: "VARCHAR(20)"
      semester: "VARCHAR(20)"

      # 开课信息
      course_location_code: "VARCHAR(10)"
      course_location_type: "VARCHAR(20)"
      time_flag: "VARCHAR(20)"
      course_weeks: "VARCHAR(100)"
      course_weeks_detail: "VARCHAR(200)"

      # 课程时间信息
      course_time_code: "VARCHAR(20)"
      course_time: "VARCHAR(20)"
      course_time_detail: "VARCHAR(100)"
      start_time: "TIME"
      end_time: "TIME"
      weekday: "VARCHAR(5)"

      # 排课信息
      schedule_category: "VARCHAR(50)"
      substitute_flag: "VARCHAR(10)"
      substitute_ip: "VARCHAR(50)"
      substitute_teacher_id: "VARCHAR(50)"

      # 教师信息
      teacher_id: "VARCHAR(50)"
      old_teacher_id: "VARCHAR(50)"
      is_record_deleted: "VARCHAR(5)"

      # 课表基础信息
      course_basic_id: "VARCHAR(50)"

      # 操作信息
      operator: "VARCHAR(50)"
      operation_time: "VARCHAR(50)"
      create_time: "VARCHAR(50)"
      update_time: "VARCHAR(50)"

      # 排课班级
      schedule_class: "VARCHAR(100)"

      # 录播相关
      recording_reason: "VARCHAR(100)"
      link_name: "VARCHAR(100)"
      qr_code_name: "VARCHAR(100)"
      qr_code_address: "VARCHAR(200)"
      link_type: "VARCHAR(20)"

      # 实验室相关
      is_special_lab: "VARCHAR(5)"
      lab_reason_id: "VARCHAR(50)"

      # 上课时间
      actual_start_time: "TIME"
      actual_end_time: "TIME"

      # 系统字段
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"

    # 字段注释说明
    field_comments:
      # 基本标识信息
      schedule_id: "排课ID"
      semester_id: "学年学期ID"
      semester: "学年学期"

      # 开课信息
      course_location_code: "开课地点代码"
      course_location_type: "开课地点类别"
      time_flag: "时间标志"
      course_weeks: "开课周次"
      course_weeks_detail: "开课周次明细"

      # 课程时间信息
      course_time_code: "课程时间编号"
      course_time: "课程时间"
      course_time_detail: "课程时间明细"
      start_time: "开始时间"
      end_time: "结束时间"
      weekday: "星期"

      # 排课信息
      schedule_category: "排课类别"
      substitute_flag: "代课标志"
      substitute_ip: "代课IP"
      substitute_teacher_id: "代课教师ID"

      # 教师信息
      teacher_id: "教师ID"
      old_teacher_id: "原教师ID"
      is_record_deleted: "记录是否删除"

      # 课表基础信息
      course_basic_id: "课表基础描述ID"

      # 操作信息
      operator: "操作人"
      operation_time: "操作时间"
      create_time: "创建时间"
      update_time: "更新时间"

      # 排课班级
      schedule_class: "排课班级"

      # 录播相关
      recording_reason: "录播原因"
      link_name: "链接名称"
      qr_code_name: "二维码名称"
      qr_code_address: "二维码地址"
      link_type: "链接类型"

      # 实验室相关
      is_special_lab: "是否特殊实验室"
      lab_reason_id: "实验室原因ID"

      # 上课时间
      actual_start_time: "实际开始时间"
      actual_end_time: "实际结束时间"

      # 系统字段
      created_at: "记录创建时间"
      updated_at: "记录更新时间"

    sync_interval: 1800  # 每30分钟同步一次（课程数据变化较频繁）
    enabled: true

  # H3C科研项目表接口
  - name: "research_project"
    description: "科研项目表同步"
    url: "https://mock.fangcloud.net/1033769420954/api/ky/fdm_kygl_kjxm"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"

    table_name: "research_project"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      # 基本标识信息
      wid: "project_id"               # 项目ID
      xmbh: "project_number"          # 项目编号
      xmmc: "project_name"            # 项目名称
      xmpzh: "project_approval_number" # 项目批准号

      # 项目分类信息
      kydlm: "research_category_code" # 科研大类码
      kydlm_mc: "research_category_name" # 科研大类名称
      xmflm: "project_type_code"      # 项目分类码
      xmflm_mc: "project_type_name"   # 项目分类名称
      xmjbm: "project_level_code"     # 项目级别码
      xmjbm_mc: "project_level_name"  # 项目级别名称
      xmlbm: "project_sub_category_code" # 项目类别码
      xmlbm_mc: "project_sub_category_name" # 项目类别名称

      # 项目状态和描述
      xmzt: "project_status"          # 项目状态
      xmjj: "project_description"     # 项目简介
      ztc: "total_funding"            # 总投资

      # 时间信息
      lxrq_jybbgxky0101023: "approval_date" # 立项日期
      ksrq_jybbgxky0101004: "start_date"    # 开始日期
      jxrq: "end_date"                      # 结束日期
      jhksrq: "planned_start_date"          # 计划开始日期
      jhwcrq: "planned_end_date"            # 计划完成日期

      # 项目负责人信息
      xmfzrh: "project_leader_code"   # 项目负责人号
      xmfzrh_mc: "project_leader_name" # 项目负责人名称
      xmfzrszdwm: "leader_dept_code"  # 项目负责人所在单位码
      xmfzrszdwmc: "leader_dept_name" # 项目负责人所在单位名称
      fzrdh: "leader_phone"           # 负责人电话
      fzryx: "leader_email"           # 负责人邮箱

      # 项目联系人信息
      xmlxrzgh: "contact_job_number"  # 项目联系人职工号
      xmlxrxm: "contact_name"         # 项目联系人姓名
      xmlxrdh: "contact_phone"        # 项目联系人电话
      xmlxryx: "contact_email"        # 项目联系人邮箱

      # 委托单位信息
      xmwtdw: "entrusting_unit"       # 项目委托单位
      gsdwm: "company_unit_code"      # 公司单位码
      gsdwm_mc: "company_unit_name"   # 公司单位名称
      gsjdptm: "company_platform_code" # 公司阶段平台码
      gsjdptm_mc: "company_platform_name" # 公司阶段平台名称

      # 学科分类信息
      yjlbm: "research_type_code"     # 研究类别码
      yjlbm_mc: "research_type_name"  # 研究类别名称
      yjxkm: "research_discipline_code" # 研究学科码
      yjxkm_mc: "research_discipline_name" # 研究学科名称
      ejxkm: "secondary_discipline_code" # 二级学科码
      ejxkm_mc: "secondary_discipline_name" # 二级学科名称
      xmejlbm: "project_secondary_category_code" # 项目二级类别码
      xmejlbm_mc: "project_secondary_category_name" # 项目二级类别名称

      # 经费信息
      htjf: "contract_funding"        # 合同经费
      ptjf: "platform_funding"        # 平台经费
      rjjf: "software_funding"        # 软件经费
      sbjf: "equipment_funding"       # 设备经费
      zcjf: "asset_funding"           # 资产经费

      # 项目执行信息
      xmzxztm: "execution_status_code" # 项目执行状态码
      xmzxztm_mc: "execution_status_name" # 项目执行状态名称
      jhwcqkm: "planned_completion_code" # 计划完成情况码
      jhwcqkm_mc: "planned_completion_name" # 计划完成情况名称
      yqyjcgjxs: "expected_research_results" # 预期研究成果形式

      # 合作信息
      hzxsm: "cooperation_form_code"  # 合作形式码
      hzxsm_mc: "cooperation_form_name" # 合作形式名称
      hzgjdqm: "cooperation_country_code" # 合作国家地区码
      hzgjdqm_mc: "cooperation_country_name" # 合作国家地区名称

      # 其他分类信息
      xmlym: "project_source_code"    # 项目来源码
      xmlym_mc: "project_source_name" # 项目来源名称
      cyfsm: "participation_form_code" # 参与方式码
      cyfsm_mc: "participation_form_name" # 参与方式名称
      wtdwgjm: "entrusting_country_code" # 委托单位国家码
      wtdwgjm_mc: "entrusting_country_name" # 委托单位国家名称
      wtdwszdqm: "entrusting_region_code" # 委托单位所在地区码
      wtdwszdqm_mc: "entrusting_region_name" # 委托单位所在地区名称

      # 社会经济信息
      mjm: "subject_code"             # 门类码
      mjm_mc: "subject_name"          # 门类名称
      shjjxym: "social_economic_code" # 社会经济效益码
      shjjxym_mc: "social_economic_name" # 社会经济效益名称
      xklym: "discipline_source_code" # 学科来源码
      xklym_mc: "discipline_source_name" # 学科来源名称
      ssjsly: "technology_source"     # 所属技术领域
      fwxym: "service_field_code"     # 服务领域码
      fwxym_mc: "service_field_name"  # 服务领域名称
      zzxsm: "organization_form_code" # 组织形式码
      zzxsm_mc: "organization_form_name" # 组织形式名称

      # 统计信息
      xmlym_tj: "project_source_stat_code" # 项目来源统计码
      xmlym_mc_tj: "project_source_stat_name" # 项目来源统计名称
      shjjmbm: "social_economic_target_code" # 社会经济目标码
      shjjmbm_mc: "social_economic_target_name" # 社会经济目标名称
      sshym: "social_science_code"    # 社会科学码
      sshym_mc: "social_science_name" # 社会科学名称
      xkmlkjm: "discipline_category_code" # 学科门类科技码
      xkmlkjm_mc: "discipline_category_name" # 学科门类科技名称

      # 活动和执行信息
      hdlxm: "activity_type_code"     # 活动类型码
      hdlxm_mc: "activity_type_name"  # 活动类型名称
      xmjtxsm: "project_stage_code"   # 项目阶段形式码
      xmjtxsm_mc: "project_stage_name" # 项目阶段形式名称
      sszkt: "research_team"          # 所属组课题

      # 合同和排名信息
      htbh: "contract_number"         # 合同编号
      xxpm: "school_ranking"          # 学校排名
      cyrs: "participants_count"      # 参与人数
      cymd: "participation_density"   # 参与密度

      # 操作和管理信息
      bz: "remarks"                   # 备注
      clrq: "processing_date"         # 处理日期
      czlx: "operation_type"          # 操作类型
      sjly: "data_source"             # 数据来源
      sbrq: "submission_date"         # 申报日期
      dwjsm: "unit_level_code"        # 单位级别码
      dwjsm_mc: "unit_level_name"     # 单位级别名称
      sbxmh: "submission_project_number" # 申报项目号
      xmzy: "project_summary"         # 项目摘要
      xdwh: "coordination_unit_number" # 协调单位号
      ktrq: "start_topic_date"        # 开题日期
      shjjmb: "social_economic_target" # 社会经济目标
      xmlydw: "project_source_unit"   # 项目来源单位
      cyjsrs: "participating_teachers" # 参与教师人数
      cyxsrs: "participating_students" # 参与学生人数
      dwh: "unit_number"              # 单位号
      dwmc: "unit_name"               # 单位名称
      zxbh: "execution_number"        # 执行编号
      zgbm: "competent_department"    # 主管部门

      # 系统字段
      etl_flag: "etl_flag"            # ETL标志
      start_time: "start_time"        # 开始时间
      end_time: "end_time"            # 结束时间
      by1: "reserved_field1"          # 备用字段1
      by2: "reserved_field2"          # 备用字段2

    # 根据实际数据类型定义的字段类型
    field_types:
      # 基本标识信息
      project_id: "VARCHAR(50) PRIMARY KEY"
      project_number: "VARCHAR(50)"
      project_name: "VARCHAR(200) NOT NULL"
      project_approval_number: "VARCHAR(50)"

      # 项目分类信息
      research_category_code: "VARCHAR(10)"
      research_category_name: "VARCHAR(50)"
      project_type_code: "VARCHAR(10)"
      project_type_name: "VARCHAR(50)"
      project_level_code: "VARCHAR(20)"
      project_level_name: "VARCHAR(100)"
      project_sub_category_code: "VARCHAR(20)"
      project_sub_category_name: "VARCHAR(100)"

      # 项目状态和描述
      project_status: "VARCHAR(50)"
      project_description: "TEXT"
      total_funding: "VARCHAR(50)"

      # 时间信息
      approval_date: "DATE"
      start_date: "DATE"
      end_date: "DATE"
      planned_start_date: "DATE"
      planned_end_date: "DATE"

      # 项目负责人信息
      project_leader_code: "VARCHAR(50)"
      project_leader_name: "VARCHAR(100)"
      leader_dept_code: "VARCHAR(50)"
      leader_dept_name: "VARCHAR(200)"
      leader_phone: "VARCHAR(20)"
      leader_email: "VARCHAR(100)"

      # 项目联系人信息
      contact_job_number: "VARCHAR(50)"
      contact_name: "VARCHAR(100)"
      contact_phone: "VARCHAR(20)"
      contact_email: "VARCHAR(100)"

      # 委托单位信息
      entrusting_unit: "VARCHAR(200)"
      company_unit_code: "VARCHAR(50)"
      company_unit_name: "VARCHAR(200)"
      company_platform_code: "VARCHAR(50)"
      company_platform_name: "VARCHAR(200)"

      # 学科分类信息
      research_type_code: "VARCHAR(20)"
      research_type_name: "VARCHAR(100)"
      research_discipline_code: "VARCHAR(20)"
      research_discipline_name: "VARCHAR(100)"
      secondary_discipline_code: "VARCHAR(20)"
      secondary_discipline_name: "VARCHAR(100)"
      project_secondary_category_code: "VARCHAR(20)"
      project_secondary_category_name: "VARCHAR(100)"

      # 经费信息
      contract_funding: "VARCHAR(50)"
      platform_funding: "VARCHAR(50)"
      software_funding: "VARCHAR(50)"
      equipment_funding: "VARCHAR(50)"
      asset_funding: "VARCHAR(50)"

      # 项目执行信息
      execution_status_code: "VARCHAR(20)"
      execution_status_name: "VARCHAR(100)"
      planned_completion_code: "VARCHAR(20)"
      planned_completion_name: "VARCHAR(100)"
      expected_research_results: "TEXT"

      # 合作信息
      cooperation_form_code: "VARCHAR(20)"
      cooperation_form_name: "VARCHAR(100)"
      cooperation_country_code: "VARCHAR(20)"
      cooperation_country_name: "VARCHAR(100)"

      # 其他分类信息
      project_source_code: "VARCHAR(20)"
      project_source_name: "VARCHAR(100)"
      participation_form_code: "VARCHAR(20)"
      participation_form_name: "VARCHAR(100)"
      entrusting_country_code: "VARCHAR(20)"
      entrusting_country_name: "VARCHAR(100)"
      entrusting_region_code: "VARCHAR(20)"
      entrusting_region_name: "VARCHAR(100)"

      # 社会经济信息
      subject_code: "VARCHAR(20)"
      subject_name: "VARCHAR(100)"
      social_economic_code: "VARCHAR(20)"
      social_economic_name: "VARCHAR(100)"
      discipline_source_code: "VARCHAR(20)"
      discipline_source_name: "VARCHAR(100)"
      technology_source: "VARCHAR(200)"
      service_field_code: "VARCHAR(20)"
      service_field_name: "VARCHAR(100)"
      organization_form_code: "VARCHAR(20)"
      organization_form_name: "VARCHAR(100)"

      # 统计信息
      project_source_stat_code: "VARCHAR(20)"
      project_source_stat_name: "VARCHAR(100)"
      social_economic_target_code: "VARCHAR(20)"
      social_economic_target_name: "VARCHAR(100)"
      social_science_code: "VARCHAR(20)"
      social_science_name: "VARCHAR(100)"
      discipline_category_code: "VARCHAR(20)"
      discipline_category_name: "VARCHAR(100)"

      # 活动和执行信息
      activity_type_code: "VARCHAR(20)"
      activity_type_name: "VARCHAR(100)"
      project_stage_code: "VARCHAR(20)"
      project_stage_name: "VARCHAR(100)"
      research_team: "VARCHAR(200)"

      # 合同和排名信息
      contract_number: "VARCHAR(50)"
      school_ranking: "VARCHAR(20)"
      participants_count: "INT"
      participation_density: "VARCHAR(50)"

      # 操作和管理信息
      remarks: "TEXT"
      processing_date: "DATE"
      operation_type: "VARCHAR(50)"
      data_source: "VARCHAR(100)"
      submission_date: "DATE"
      unit_level_code: "VARCHAR(20)"
      unit_level_name: "VARCHAR(100)"
      submission_project_number: "VARCHAR(50)"
      project_summary: "TEXT"
      coordination_unit_number: "VARCHAR(50)"
      start_topic_date: "DATE"
      social_economic_target: "VARCHAR(200)"
      project_source_unit: "VARCHAR(200)"
      participating_teachers: "INT"
      participating_students: "INT"
      unit_number: "VARCHAR(50)"
      unit_name: "VARCHAR(200)"
      execution_number: "VARCHAR(50)"
      competent_department: "VARCHAR(100)"

      # 系统字段
      etl_flag: "VARCHAR(20)"
      start_time: "VARCHAR(50)"
      end_time: "VARCHAR(50)"
      reserved_field1: "VARCHAR(200)"
      reserved_field2: "VARCHAR(200)"
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"

    # 字段注释说明
    field_comments:
      # 基本标识信息
      project_id: "项目ID"
      project_number: "项目编号"
      project_name: "项目名称"
      project_approval_number: "项目批准号"

      # 项目分类信息
      research_category_code: "科研大类码"
      research_category_name: "科研大类名称"
      project_type_code: "项目分类码"
      project_type_name: "项目分类名称"
      project_level_code: "项目级别码"
      project_level_name: "项目级别名称"
      project_sub_category_code: "项目类别码"
      project_sub_category_name: "项目类别名称"

      # 项目状态和描述
      project_status: "项目状态"
      project_description: "项目简介"
      total_funding: "总投资"

      # 时间信息
      approval_date: "立项日期"
      start_date: "开始日期"
      end_date: "结束日期"
      planned_start_date: "计划开始日期"
      planned_end_date: "计划完成日期"

      # 项目负责人信息
      project_leader_code: "项目负责人号"
      project_leader_name: "项目负责人名称"
      leader_dept_code: "项目负责人所在单位码"
      leader_dept_name: "项目负责人所在单位名称"
      leader_phone: "负责人电话"
      leader_email: "负责人邮箱"

      # 项目联系人信息
      contact_job_number: "项目联系人职工号"
      contact_name: "项目联系人姓名"
      contact_phone: "项目联系人电话"
      contact_email: "项目联系人邮箱"

      # 委托单位信息
      entrusting_unit: "项目委托单位"
      company_unit_code: "公司单位码"
      company_unit_name: "公司单位名称"
      company_platform_code: "公司阶段平台码"
      company_platform_name: "公司阶段平台名称"

      # 学科分类信息
      research_type_code: "研究类别码"
      research_type_name: "研究类别名称"
      research_discipline_code: "研究学科码"
      research_discipline_name: "研究学科名称"
      secondary_discipline_code: "二级学科码"
      secondary_discipline_name: "二级学科名称"
      project_secondary_category_code: "项目二级类别码"
      project_secondary_category_name: "项目二级类别名称"

      # 经费信息
      contract_funding: "合同经费"
      platform_funding: "平台经费"
      software_funding: "软件经费"
      equipment_funding: "设备经费"
      asset_funding: "资产经费"

      # 项目执行信息
      execution_status_code: "项目执行状态码"
      execution_status_name: "项目执行状态名称"
      planned_completion_code: "计划完成情况码"
      planned_completion_name: "计划完成情况名称"
      expected_research_results: "预期研究成果形式"

      # 合作信息
      cooperation_form_code: "合作形式码"
      cooperation_form_name: "合作形式名称"
      cooperation_country_code: "合作国家地区码"
      cooperation_country_name: "合作国家地区名称"

      # 其他分类信息
      project_source_code: "项目来源码"
      project_source_name: "项目来源名称"
      participation_form_code: "参与方式码"
      participation_form_name: "参与方式名称"
      entrusting_country_code: "委托单位国家码"
      entrusting_country_name: "委托单位国家名称"
      entrusting_region_code: "委托单位所在地区码"
      entrusting_region_name: "委托单位所在地区名称"

      # 社会经济信息
      subject_code: "门类码"
      subject_name: "门类名称"
      social_economic_code: "社会经济效益码"
      social_economic_name: "社会经济效益名称"
      discipline_source_code: "学科来源码"
      discipline_source_name: "学科来源名称"
      technology_source: "所属技术领域"
      service_field_code: "服务领域码"
      service_field_name: "服务领域名称"
      organization_form_code: "组织形式码"
      organization_form_name: "组织形式名称"

      # 统计信息
      project_source_stat_code: "项目来源统计码"
      project_source_stat_name: "项目来源统计名称"
      social_economic_target_code: "社会经济目标码"
      social_economic_target_name: "社会经济目标名称"
      social_science_code: "社会科学码"
      social_science_name: "社会科学名称"
      discipline_category_code: "学科门类科技码"
      discipline_category_name: "学科门类科技名称"

      # 活动和执行信息
      activity_type_code: "活动类型码"
      activity_type_name: "活动类型名称"
      project_stage_code: "项目阶段形式码"
      project_stage_name: "项目阶段形式名称"
      research_team: "所属组课题"

      # 合同和排名信息
      contract_number: "合同编号"
      school_ranking: "学校排名"
      participants_count: "参与人数"
      participation_density: "参与密度"

      # 操作和管理信息
      remarks: "备注"
      processing_date: "处理日期"
      operation_type: "操作类型"
      data_source: "数据来源"
      submission_date: "申报日期"
      unit_level_code: "单位级别码"
      unit_level_name: "单位级别名称"
      submission_project_number: "申报项目号"
      project_summary: "项目摘要"
      coordination_unit_number: "协调单位号"
      start_topic_date: "开题日期"
      social_economic_target: "社会经济目标"
      project_source_unit: "项目来源单位"
      participating_teachers: "参与教师人数"
      participating_students: "参与学生人数"
      unit_number: "单位号"
      unit_name: "单位名称"
      execution_number: "执行编号"
      competent_department: "主管部门"

      # 系统字段
      etl_flag: "ETL标志"
      start_time: "开始时间"
      end_time: "结束时间"
      reserved_field1: "备用字段1"
      reserved_field2: "备用字段2"
      created_at: "记录创建时间"
      updated_at: "记录更新时间"

    sync_interval: 3600  # 每小时同步一次
    enabled: true

  # H3C学籍异动表接口
  - name: "student_status_change"
    description: "学籍异动表同步"
    url: "https://mock.fangcloud.net/1033769420954/api/v1/fdm/gxxs/fdm_gxxs_xjyd"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    # 分页参数配置
    params:
      pageSize: "100"  # 每页记录数
      pageNum: "1"     # 起始页码（程序会自动递增）
    table_name: "student_status_change"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      # 基本标识信息
      id: "record_id"                 # 记录ID
      yxh: "original_student_number"  # 原学号
      xxh: "new_student_number"       # 新学号
      xm: "student_name"              # 学生姓名

      # 异动信息
      ydlbm: "change_type_code"       # 异动类别码
      ydlb: "change_type"             # 异动类别
      ydrq: "change_date"             # 异动日期
      ydyym: "change_reason_code"     # 异动原因码
      ydyy: "change_reason"           # 异动原因
      sprq: "approval_date"           # 审批日期
      spwh: "approval_document"       # 审批文号
      ydlyxxm: "change_source_code"   # 异动来源学校码
      ydqxxxm: "change_target_code"   # 异动去向学校码
      ydsm: "change_description"      # 异动说明

      # 原学籍信息
      yyxsh: "original_college_code"  # 原院系号
      yyxsmc: "original_college_name" # 原院系名称
      yzym: "original_major_code"     # 原专业码
      yzymc: "original_major_name"    # 原专业名称
      ybh: "original_class_code"      # 原班号
      ybj: "original_class_name"      # 原班级
      ynj: "original_grade"           # 原年级
      yxz: "original_study_years"     # 原学制
      yxjztm: "original_status_code"  # 原学籍状态码
      yxjzt: "original_status"        # 原学籍状态
      yxsdqztm: "original_current_status_code" # 原学生当前状态码
      yxsdqzt: "original_current_status"       # 原学生当前状态

      # 新学籍信息
      xyxsh: "new_college_code"       # 新院系号
      xyxsmc: "new_college_name"      # 新院系名称
      xzym: "new_major_code"          # 新专业码
      xzymc: "new_major_name"         # 新专业名称
      xbh: "new_class_code"           # 新班号
      xbj: "new_class_name"           # 新班级
      xnj: "new_grade"                # 新年级
      xxz: "new_study_years"          # 新学制
      xxjztm: "new_status_code"       # 新学籍状态码
      xxjzt: "new_status"             # 新学籍状态
      xxsdqztm: "new_current_status_code"  # 新学生当前状态码
      xxsdqzt: "new_current_status"        # 新学生当前状态

      # 学期和状态信息
      sxxnxq: "semester"              # 生效学年学期
      ydfs: "change_method"           # 异动方式
      sqly: "application_reason"      # 申请理由
      zszt: "approval_status"         # 审批状态
      fwrq: "return_date"             # 复学日期
      zxztm: "school_status_code"     # 在校状态码
      zxzt: "school_status"           # 在校状态
      oldzxztm: "old_school_status_code" # 原在校状态码
      oldzxzt: "old_school_status"       # 原在校状态

      # 毕业专业信息
      ybyzy: "original_graduate_major" # 原毕业专业
      xbyzy: "new_graduate_major"      # 新毕业专业
      ybyrq: "original_graduate_date"  # 原毕业日期
      xbyrq: "new_graduate_date"       # 新毕业日期

      # 国际交流信息
      ygjxj: "original_exchange_level" # 原国际交流级
      xgjxj: "new_exchange_level"      # 新国际交流级

      # 时间信息
      ydkssj: "change_start_time"     # 异动开始时间
      ydjssj: "change_end_time"       # 异动结束时间
      xxksrq: "study_start_date"      # 学习开始日期
      xxjsrq: "study_end_date"        # 学习结束日期
      jlkssj: "record_start_time"     # 记录开始时间
      jljssj: "record_end_time"       # 记录结束时间
      jldd: "record_location"         # 记录地点

      # 附件信息
      xsydfjname: "student_change_file_name" # 学生异动附件名
      xsydfjpath: "student_change_file_path" # 学生异动附件路径

      # 审核信息
      fdysh: "counselor_review"       # 辅导员审核
      xldsh: "dean_review"            # 学院领导审核
      xjglysh: "registrar_review"     # 学籍管理员审核
      fdyshspr: "counselor_reviewer"  # 辅导员审核人
      fdyshsj: "counselor_review_time" # 辅导员审核时间
      xldshspr: "dean_reviewer"       # 学院领导审核人
      xldshsj: "dean_review_time"     # 学院领导审核时间
      xjglyshspr: "registrar_reviewer" # 学籍管理员审核人
      xjglyshsj: "registrar_review_time" # 学籍管理员审核时间

      # 处理状态
      clzt: "process_status"          # 处理状态
      newclzt: "new_process_status"   # 新处理状态
      blzt: "handle_status"           # 办理状态

      # 院系审核
      szyxsh: "college_review"        # 所在院系审核
      szyxspr: "college_reviewer"     # 所在院系审核人
      szyxspsj: "college_review_time" # 所在院系审核时间
      zryxsh: "target_college_review" # 转入院系审核
      zryxspr: "target_college_reviewer" # 转入院系审核人
      zryxspsj: "target_college_review_time" # 转入院系审核时间

      # 教务处审核
      jwcsh: "academic_office_review" # 教务处审核
      jwcspr: "academic_office_reviewer" # 教务处审核人
      jwcspsj: "academic_office_review_time" # 教务处审核时间
      jwcshfjname: "academic_office_file_name" # 教务处审核附件名
      jwcshfjpath: "academic_office_file_path" # 教务处审核附件路径

      # 其他附件
      zrfjname: "transfer_file_name"  # 转入附件名
      zrfjpath: "transfer_file_path"  # 转入附件路径
      szfjname: "college_file_name"   # 所在院系附件名
      szfjpath: "college_file_path"   # 所在院系附件路径

      # 其他信息
      zcxdnx: "transfer_grade_year"   # 转出/转入年级
      yjbysj: "expected_graduate_time" # 预计毕业时间
      zcxyq: "transfer_requirement"   # 转出/转入要求
      babj: "class_arrangement"       # 班级安排
      sfxxwyd: "is_school_internal_change" # 是否校内异动
      blrxzgkssj: "handle_start_time" # 办理入学资格开始时间
      blrxzgjssj: "handle_end_time"   # 办理入学资格结束时间
      sfpl: "is_batch_process"        # 是否批量
      wbshzt: "external_review_status" # 外部审核状态

    # 根据实际数据类型定义的字段类型
    field_types:
      # 基本标识信息
      record_id: "VARCHAR(50) PRIMARY KEY"
      original_student_number: "VARCHAR(20)"
      new_student_number: "VARCHAR(20)"
      student_name: "VARCHAR(50) NOT NULL"

      # 异动信息
      change_type_code: "VARCHAR(10)"
      change_type: "VARCHAR(50)"
      change_date: "DATE"
      change_reason_code: "VARCHAR(10)"
      change_reason: "VARCHAR(100)"
      approval_date: "DATE"
      approval_document: "VARCHAR(100)"
      change_source_code: "VARCHAR(50)"
      change_target_code: "VARCHAR(50)"
      change_description: "TEXT"

      # 原学籍信息
      original_college_code: "VARCHAR(20)"
      original_college_name: "VARCHAR(100)"
      original_major_code: "VARCHAR(50)"
      original_major_name: "VARCHAR(100)"
      original_class_code: "VARCHAR(50)"
      original_class_name: "VARCHAR(100)"
      original_grade: "VARCHAR(10)"
      original_study_years: "VARCHAR(10)"
      original_status_code: "VARCHAR(10)"
      original_status: "VARCHAR(30)"
      original_current_status_code: "VARCHAR(10)"
      original_current_status: "VARCHAR(30)"

      # 新学籍信息
      new_college_code: "VARCHAR(20)"
      new_college_name: "VARCHAR(100)"
      new_major_code: "VARCHAR(50)"
      new_major_name: "VARCHAR(100)"
      new_class_code: "VARCHAR(50)"
      new_class_name: "VARCHAR(100)"
      new_grade: "VARCHAR(10)"
      new_study_years: "VARCHAR(10)"
      new_status_code: "VARCHAR(10)"
      new_status: "VARCHAR(30)"
      new_current_status_code: "VARCHAR(10)"
      new_current_status: "VARCHAR(30)"

      # 学期和状态信息
      semester: "VARCHAR(20)"
      change_method: "VARCHAR(50)"
      application_reason: "VARCHAR(200)"
      approval_status: "VARCHAR(20)"
      return_date: "DATE"
      school_status_code: "VARCHAR(10)"
      school_status: "VARCHAR(30)"
      old_school_status_code: "VARCHAR(10)"
      old_school_status: "VARCHAR(30)"

      # 毕业专业信息
      original_graduate_major: "VARCHAR(50)"
      new_graduate_major: "VARCHAR(50)"
      original_graduate_date: "DATE"
      new_graduate_date: "DATE"

      # 国际交流信息
      original_exchange_level: "VARCHAR(50)"
      new_exchange_level: "VARCHAR(50)"

      # 时间信息
      change_start_time: "VARCHAR(50)"
      change_end_time: "VARCHAR(50)"
      study_start_date: "DATE"
      study_end_date: "DATE"
      record_start_time: "VARCHAR(50)"
      record_end_time: "VARCHAR(50)"
      record_location: "VARCHAR(100)"

      # 附件信息
      student_change_file_name: "VARCHAR(200)"
      student_change_file_path: "VARCHAR(500)"

      # 审核信息
      counselor_review: "VARCHAR(20)"
      dean_review: "VARCHAR(20)"
      registrar_review: "VARCHAR(20)"
      counselor_reviewer: "VARCHAR(50)"
      counselor_review_time: "VARCHAR(50)"
      dean_reviewer: "VARCHAR(50)"
      dean_review_time: "VARCHAR(50)"
      registrar_reviewer: "VARCHAR(50)"
      registrar_review_time: "VARCHAR(50)"

      # 处理状态
      process_status: "VARCHAR(20)"
      new_process_status: "VARCHAR(20)"
      handle_status: "VARCHAR(20)"

      # 院系审核
      college_review: "VARCHAR(20)"
      college_reviewer: "VARCHAR(50)"
      college_review_time: "VARCHAR(50)"
      target_college_review: "VARCHAR(20)"
      target_college_reviewer: "VARCHAR(50)"
      target_college_review_time: "VARCHAR(50)"

      # 教务处审核
      academic_office_review: "VARCHAR(20)"
      academic_office_reviewer: "VARCHAR(50)"
      academic_office_review_time: "VARCHAR(50)"
      academic_office_file_name: "VARCHAR(200)"
      academic_office_file_path: "VARCHAR(500)"

      # 其他附件
      transfer_file_name: "VARCHAR(200)"
      transfer_file_path: "VARCHAR(500)"
      college_file_name: "VARCHAR(200)"
      college_file_path: "VARCHAR(500)"

      # 其他信息
      transfer_grade_year: "VARCHAR(20)"
      expected_graduate_time: "VARCHAR(50)"
      transfer_requirement: "VARCHAR(200)"
      class_arrangement: "VARCHAR(100)"
      is_school_internal_change: "VARCHAR(5)"
      handle_start_time: "VARCHAR(50)"
      handle_end_time: "VARCHAR(50)"
      is_batch_process: "VARCHAR(5)"
      external_review_status: "VARCHAR(20)"

      # 系统字段
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"

    # 字段注释说明
    field_comments:
      # 基本标识信息
      record_id: "记录ID"
      original_student_number: "原学号"
      new_student_number: "新学号"
      student_name: "学生姓名"

      # 异动信息
      change_type_code: "异动类别码"
      change_type: "异动类别"
      change_date: "异动日期"
      change_reason_code: "异动原因码"
      change_reason: "异动原因"
      approval_date: "审批日期"
      approval_document: "审批文号"
      change_source_code: "异动来源学校码"
      change_target_code: "异动去向学校码"
      change_description: "异动说明"

      # 原学籍信息
      original_college_code: "原院系号"
      original_college_name: "原院系名称"
      original_major_code: "原专业码"
      original_major_name: "原专业名称"
      original_class_code: "原班号"
      original_class_name: "原班级"
      original_grade: "原年级"
      original_study_years: "原学制"
      original_status_code: "原学籍状态码"
      original_status: "原学籍状态"
      original_current_status_code: "原学生当前状态码"
      original_current_status: "原学生当前状态"

      # 新学籍信息
      new_college_code: "新院系号"
      new_college_name: "新院系名称"
      new_major_code: "新专业码"
      new_major_name: "新专业名称"
      new_class_code: "新班号"
      new_class_name: "新班级"
      new_grade: "新年级"
      new_study_years: "新学制"
      new_status_code: "新学籍状态码"
      new_status: "新学籍状态"
      new_current_status_code: "新学生当前状态码"
      new_current_status: "新学生当前状态"

      # 学期和状态信息
      semester: "生效学年学期"
      change_method: "异动方式"
      application_reason: "申请理由"
      approval_status: "审批状态"
      return_date: "复学日期"
      school_status_code: "在校状态码"
      school_status: "在校状态"
      old_school_status_code: "原在校状态码"
      old_school_status: "原在校状态"

      # 毕业专业信息
      original_graduate_major: "原毕业专业"
      new_graduate_major: "新毕业专业"
      original_graduate_date: "原毕业日期"
      new_graduate_date: "新毕业日期"

      # 国际交流信息
      original_exchange_level: "原国际交流级"
      new_exchange_level: "新国际交流级"

      # 时间信息
      change_start_time: "异动开始时间"
      change_end_time: "异动结束时间"
      study_start_date: "学习开始日期"
      study_end_date: "学习结束日期"
      record_start_time: "记录开始时间"
      record_end_time: "记录结束时间"
      record_location: "记录地点"

      # 附件信息
      student_change_file_name: "学生异动附件名"
      student_change_file_path: "学生异动附件路径"

      # 审核信息
      counselor_review: "辅导员审核"
      dean_review: "学院领导审核"
      registrar_review: "学籍管理员审核"
      counselor_reviewer: "辅导员审核人"
      counselor_review_time: "辅导员审核时间"
      dean_reviewer: "学院领导审核人"
      dean_review_time: "学院领导审核时间"
      registrar_reviewer: "学籍管理员审核人"
      registrar_review_time: "学籍管理员审核时间"

      # 处理状态
      process_status: "处理状态"
      new_process_status: "新处理状态"
      handle_status: "办理状态"

      # 院系审核
      college_review: "所在院系审核"
      college_reviewer: "所在院系审核人"
      college_review_time: "所在院系审核时间"
      target_college_review: "转入院系审核"
      target_college_reviewer: "转入院系审核人"
      target_college_review_time: "转入院系审核时间"

      # 教务处审核
      academic_office_review: "教务处审核"
      academic_office_reviewer: "教务处审核人"
      academic_office_review_time: "教务处审核时间"
      academic_office_file_name: "教务处审核附件名"
      academic_office_file_path: "教务处审核附件路径"

      # 其他附件
      transfer_file_name: "转入附件名"
      transfer_file_path: "转入附件路径"
      college_file_name: "所在院系附件名"
      college_file_path: "所在院系附件路径"

      # 其他信息
      transfer_grade_year: "转出/转入年级"
      expected_graduate_time: "预计毕业时间"
      transfer_requirement: "转出/转入要求"
      class_arrangement: "班级安排"
      is_school_internal_change: "是否校内异动"
      handle_start_time: "办理入学资格开始时间"
      handle_end_time: "办理入学资格结束时间"
      is_batch_process: "是否批量"
      external_review_status: "外部审核状态"

      # 系统字段
      created_at: "记录创建时间"
      updated_at: "记录更新时间"

    sync_interval: 3600  # 每小时同步一次
    enabled: true

  # H3C学生学籍表接口
  - name: "student_registration"
    description: "学生学籍表同步"
    url: "https://mock.fangcloud.net/1033769420954/api/v1/fdm/gxxs/fdm_gxxs_xjjbsj"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"

    table_name: "student_registration"
    # 根据实际API响应结构配置的字段映射
    field_mapping:
      # 基本标识信息
      id: "record_id"                 # 记录ID
      xh: "student_number"            # 学号
      xm: "student_name"              # 学生姓名
      rxny: "enrollment_year"         # 入学年月

      # 学生类别信息
      xslbm: "student_category_code"  # 学生类别码
      xslb: "student_category"        # 学生类别

      # 学院专业班级信息
      yxdm: "college_code"            # 院系代码
      yxmc: "college_name"            # 院系名称
      zydm: "major_code"              # 专业代码
      zymc: "major_name"              # 专业名称
      bjdm: "class_code"              # 班级代码
      bjmc: "class_name"              # 班级名称

      # 学科门类信息
      xkmlm: "discipline_category_code" # 学科门类码
      xkml: "discipline_category"       # 学科门类

      # 培养信息
      pyfsm: "training_method_code"   # 培养方式码
      pyfs: "training_method"         # 培养方式
      yjfx: "research_direction"      # 研究方向
      dsh: "supervisor_number"        # 导师号
      dsxm: "supervisor_name"         # 导师姓名
      hdxlfsm: "degree_type_code"     # 获得学历方式码
      hdxlfs: "degree_type"           # 获得学历方式
      sfxfz: "is_credit_system"       # 是否学分制
      pyccm: "training_level_code"    # 培养层次码
      pycc: "training_level"          # 培养层次
      ldfsm: "enrollment_method_code" # 录取方式码
      ldfs: "enrollment_method"       # 录取方式

      # 学生状态信息
      xsdqztm: "current_status_code"  # 学生当前状态码
      xsdqzt: "current_status"        # 学生当前状态
      xxxsm: "study_form_code"        # 学习形式码
      xxxs: "study_form"              # 学习形式
      dqszj: "current_grade"          # 当前所在级
      xjztm: "registration_status_code" # 学籍状态码
      xjzt: "registration_status"     # 学籍状态
      xssftzfsm: "special_status_code" # 学生身份特征方式码
      xssftzfs: "special_status"      # 学生身份特征方式

      # 考试招生信息
      ksh: "exam_number"              # 考生号
      zsxk: "admission_subject"       # 招生学科
      fxflbbid: "minor_category_id"   # 辅修分类别ID
      fxzyid: "minor_major_id"        # 辅修专业ID

      # 毕业信息
      bysj: "graduation_time"         # 毕业时间
      zxjhid: "execution_plan_id"     # 执行计划ID
      zxztm: "school_status_code"     # 在校状态码
      zxzt: "school_status"           # 在校状态
      byzy: "graduation_major"        # 毕业专业
      fxbjid: "minor_class_id"        # 辅修班级ID
      sfsfs: "is_teacher_training"    # 是否师范生
      zcxdnx: "transfer_grade"        # 转出/转入年级
      fxbyjb: "minor_graduation_level" # 辅修毕业级别

      # 其他标识
      xsdsid: "student_file_id"       # 学生档案ID
      sfyjj: "is_scholarship"         # 是否有奖学金
      schcardid: "school_card_id"     # 校园卡ID
      ylzh: "medical_account"         # 医疗账号
      yjbysj: "expected_graduation_time" # 预计毕业时间
      gjxj: "international_exchange"  # 国际交流
      xwsysj: "degree_award_time"     # 学位授予时间
      sfyjbys: "is_expected_graduate" # 是否预计毕业生

      # 本科毕业信息
      bkbysj: "bachelor_graduation_time" # 本科毕业时间
      yjsbysj: "master_graduation_time"  # 研究生毕业时间
      rxksyz: "enrollment_test_language" # 入学考试语种
      xslx: "student_type"            # 学生类型
      zslb: "enrollment_category"     # 招生类别
      sfyd: "is_mobile"               # 是否异动

      # 学位信息
      dexw: "obtained_degree"         # 得学位
      bylxm: "graduation_type_code"   # 毕业类型码
      bylx: "graduation_type"         # 毕业类型
      byshjg: "graduation_review_result" # 毕业审核结果
      byzsbh: "graduation_certificate_number" # 毕业证书号
      jyzsbh: "completion_certificate_number" # 结业证书号
      hbyzssj: "graduation_certificate_time" # 获得毕业证书时间
      xwshjg: "degree_review_result"  # 学位审核结果
      xwzsbh: "degree_certificate_number" # 学位证书号
      syxwsj: "degree_award_time2"    # 授予学位时间

      # 辅修学位信息
      fxxsxwzsbh: "minor_degree_certificate_number" # 辅修学士学位证书号
      syfxxsxwsj: "minor_degree_award_time" # 授予辅修学士学位时间
      fxzsbh: "minor_certificate_number" # 辅修证书号
      fxzshzsj: "minor_certificate_time" # 辅修证书获得时间
      dexsxwzsbh: "second_degree_certificate_number" # 第二学士学位证书号
      sydexsxwsj: "second_degree_award_time" # 授予第二学士学位时间

      # 学位等级信息
      xw: "degree"                    # 学位
      sflb: "is_category"             # 是否类别
      sfsd: "is_master"               # 是否硕士
      zx: "specialization"            # 专项
      zxz: "specialization_certificate" # 专项证

      # 本科毕业经历
      bkbyjlm: "bachelor_experience_code" # 本科毕业经历码
      bkbyjl: "bachelor_experience"   # 本科毕业经历
      bkbyzsbh: "bachelor_certificate_number" # 本科毕业证书号
      bfbyzssj: "bachelor_certificate_time" # 本科毕业证书时间
      bkjyzbh: "bachelor_education_certificate" # 本科教育证书号
      bkjysj: "bachelor_education_time" # 本科教育时间

      # 学士学位信息
      xsxwmc: "bachelor_degree_name"  # 学士学位名称
      xsxwsysj: "bachelor_degree_award_time" # 学士学位授予时间
      bkyyzsbh: "bachelor_language_certificate" # 本科语言证书号
      bkyysj: "bachelor_language_time" # 本科语言时间

      # 其他标识
      isgfdxsfs: "is_high_level_talent" # 是否高层次人才
      yxydyzt: "college_first_choice_status" # 院校第一志愿状态
      sfsn: "is_rural_student"        # 是否农村学生
      xyqtz: "college_other_info"     # 学院其他信息
      xwzsbh2: "degree_certificate_number2" # 学位证书号2
      xsxz: "study_duration"          # 学生学制
      xspyccm: "student_training_level_code" # 学生培养层次码
      sffx: "is_minor"                # 是否辅修
      xjlbm: "registration_category_code" # 学籍类别码
      sfdxpy: "is_university_training" # 是否大学培养
      dxpydw: "university_training_unit" # 大学培养单位

    # 根据实际数据类型定义的字段类型
    field_types:
      # 基本标识信息
      record_id: "VARCHAR(50) PRIMARY KEY"
      student_number: "VARCHAR(20) NOT NULL"
      student_name: "VARCHAR(50) NOT NULL"
      enrollment_year: "VARCHAR(20)"

      # 学生类别信息
      student_category_code: "VARCHAR(20)"
      student_category: "VARCHAR(50)"

      # 学院专业班级信息
      college_code: "VARCHAR(20)"
      college_name: "VARCHAR(100)"
      major_code: "VARCHAR(20)"
      major_name: "VARCHAR(100)"
      class_code: "VARCHAR(20)"
      class_name: "VARCHAR(100)"

      # 学科门类信息
      discipline_category_code: "VARCHAR(20)"
      discipline_category: "VARCHAR(100)"

      # 培养信息
      training_method_code: "VARCHAR(20)"
      training_method: "VARCHAR(100)"
      research_direction: "VARCHAR(200)"
      supervisor_number: "VARCHAR(50)"
      supervisor_name: "VARCHAR(100)"
      degree_type_code: "VARCHAR(20)"
      degree_type: "VARCHAR(100)"
      is_credit_system: "VARCHAR(5)"
      training_level_code: "VARCHAR(20)"
      training_level: "VARCHAR(100)"
      enrollment_method_code: "VARCHAR(20)"
      enrollment_method: "VARCHAR(100)"

      # 学生状态信息
      current_status_code: "VARCHAR(10)"
      current_status: "VARCHAR(30)"
      study_form_code: "VARCHAR(20)"
      study_form: "VARCHAR(100)"
      current_grade: "VARCHAR(10)"
      registration_status_code: "VARCHAR(10)"
      registration_status: "VARCHAR(30)"
      special_status_code: "VARCHAR(20)"
      special_status: "VARCHAR(100)"

      # 考试招生信息
      exam_number: "VARCHAR(50)"
      admission_subject: "VARCHAR(100)"
      minor_category_id: "VARCHAR(50)"
      minor_major_id: "VARCHAR(50)"

      # 毕业信息
      graduation_time: "VARCHAR(50)"
      execution_plan_id: "VARCHAR(50)"
      school_status_code: "VARCHAR(10)"
      school_status: "VARCHAR(30)"
      graduation_major: "VARCHAR(100)"
      minor_class_id: "VARCHAR(50)"
      is_teacher_training: "VARCHAR(5)"
      transfer_grade: "VARCHAR(20)"
      minor_graduation_level: "VARCHAR(50)"

      # 其他标识
      student_file_id: "VARCHAR(50)"
      is_scholarship: "VARCHAR(5)"
      school_card_id: "VARCHAR(50)"
      medical_account: "VARCHAR(50)"
      expected_graduation_time: "VARCHAR(50)"
      international_exchange: "VARCHAR(100)"
      degree_award_time: "VARCHAR(50)"
      is_expected_graduate: "VARCHAR(5)"

      # 本科毕业信息
      bachelor_graduation_time: "VARCHAR(50)"
      master_graduation_time: "VARCHAR(50)"
      enrollment_test_language: "VARCHAR(50)"
      student_type: "VARCHAR(50)"
      enrollment_category: "VARCHAR(50)"
      is_mobile: "VARCHAR(5)"

      # 学位信息
      obtained_degree: "VARCHAR(50)"
      graduation_type_code: "VARCHAR(20)"
      graduation_type: "VARCHAR(50)"
      graduation_review_result: "VARCHAR(50)"
      graduation_certificate_number: "VARCHAR(100)"
      completion_certificate_number: "VARCHAR(100)"
      graduation_certificate_time: "VARCHAR(50)"
      degree_review_result: "VARCHAR(50)"
      degree_certificate_number: "VARCHAR(100)"
      degree_award_time2: "VARCHAR(50)"

      # 辅修学位信息
      minor_degree_certificate_number: "VARCHAR(100)"
      minor_degree_award_time: "VARCHAR(50)"
      minor_certificate_number: "VARCHAR(100)"
      minor_certificate_time: "VARCHAR(50)"
      second_degree_certificate_number: "VARCHAR(100)"
      second_degree_award_time: "VARCHAR(50)"

      # 学位等级信息
      degree: "VARCHAR(50)"
      is_category: "VARCHAR(5)"
      is_master: "VARCHAR(5)"
      specialization: "VARCHAR(100)"
      specialization_certificate: "VARCHAR(100)"

      # 本科毕业经历
      bachelor_experience_code: "VARCHAR(20)"
      bachelor_experience: "VARCHAR(100)"
      bachelor_certificate_number: "VARCHAR(100)"
      bachelor_certificate_time: "VARCHAR(50)"
      bachelor_education_certificate: "VARCHAR(100)"
      bachelor_education_time: "VARCHAR(50)"

      # 学士学位信息
      bachelor_degree_name: "VARCHAR(100)"
      bachelor_degree_award_time: "VARCHAR(50)"
      bachelor_language_certificate: "VARCHAR(100)"
      bachelor_language_time: "VARCHAR(50)"

      # 其他标识
      is_high_level_talent: "VARCHAR(5)"
      college_first_choice_status: "VARCHAR(50)"
      is_rural_student: "VARCHAR(5)"
      college_other_info: "VARCHAR(200)"
      degree_certificate_number2: "VARCHAR(100)"
      study_duration: "VARCHAR(10)"
      student_training_level_code: "VARCHAR(20)"
      is_minor: "VARCHAR(5)"
      registration_category_code: "VARCHAR(20)"
      is_university_training: "VARCHAR(5)"
      university_training_unit: "VARCHAR(200)"

      # 系统字段
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"

    # 字段注释说明
    field_comments:
      # 基本标识信息
      record_id: "记录ID"
      student_number: "学号"
      student_name: "学生姓名"
      enrollment_year: "入学年月"

      # 学生类别信息
      student_category_code: "学生类别码"
      student_category: "学生类别"

      # 学院专业班级信息
      college_code: "院系代码"
      college_name: "院系名称"
      major_code: "专业代码"
      major_name: "专业名称"
      class_code: "班级代码"
      class_name: "班级名称"

      # 学科门类信息
      discipline_category_code: "学科门类码"
      discipline_category: "学科门类"

      # 培养信息
      training_method_code: "培养方式码"
      training_method: "培养方式"
      research_direction: "研究方向"
      supervisor_number: "导师号"
      supervisor_name: "导师姓名"
      degree_type_code: "获得学历方式码"
      degree_type: "获得学历方式"
      is_credit_system: "是否学分制"
      training_level_code: "培养层次码"
      training_level: "培养层次"
      enrollment_method_code: "录取方式码"
      enrollment_method: "录取方式"

      # 学生状态信息
      current_status_code: "学生当前状态码"
      current_status: "学生当前状态"
      study_form_code: "学习形式码"
      study_form: "学习形式"
      current_grade: "当前所在级"
      registration_status_code: "学籍状态码"
      registration_status: "学籍状态"
      special_status_code: "学生身份特征方式码"
      special_status: "学生身份特征方式"

      # 考试招生信息
      exam_number: "考生号"
      admission_subject: "招生学科"
      minor_category_id: "辅修分类别ID"
      minor_major_id: "辅修专业ID"

      # 毕业信息
      graduation_time: "毕业时间"
      execution_plan_id: "执行计划ID"
      school_status_code: "在校状态码"
      school_status: "在校状态"
      graduation_major: "毕业专业"
      minor_class_id: "辅修班级ID"
      is_teacher_training: "是否师范生"
      transfer_grade: "转出/转入年级"
      minor_graduation_level: "辅修毕业级别"

      # 其他标识
      student_file_id: "学生档案ID"
      is_scholarship: "是否有奖学金"
      school_card_id: "校园卡ID"
      medical_account: "医疗账号"
      expected_graduation_time: "预计毕业时间"
      international_exchange: "国际交流"
      degree_award_time: "学位授予时间"
      is_expected_graduate: "是否预计毕业生"

      # 本科毕业信息
      bachelor_graduation_time: "本科毕业时间"
      master_graduation_time: "研究生毕业时间"
      enrollment_test_language: "入学考试语种"
      student_type: "学生类型"
      enrollment_category: "招生类别"
      is_mobile: "是否异动"

      # 学位信息
      obtained_degree: "得学位"
      graduation_type_code: "毕业类型码"
      graduation_type: "毕业类型"
      graduation_review_result: "毕业审核结果"
      graduation_certificate_number: "毕业证书号"
      completion_certificate_number: "结业证书号"
      graduation_certificate_time: "获得毕业证书时间"
      degree_review_result: "学位审核结果"
      degree_certificate_number: "学位证书号"
      degree_award_time2: "授予学位时间"

      # 辅修学位信息
      minor_degree_certificate_number: "辅修学士学位证书号"
      minor_degree_award_time: "授予辅修学士学位时间"
      minor_certificate_number: "辅修证书号"
      minor_certificate_time: "辅修证书获得时间"
      second_degree_certificate_number: "第二学士学位证书号"
      second_degree_award_time: "授予第二学士学位时间"

      # 学位等级信息
      degree: "学位"
      is_category: "是否类别"
      is_master: "是否硕士"
      specialization: "专项"
      specialization_certificate: "专项证"

      # 本科毕业经历
      bachelor_experience_code: "本科毕业经历码"
      bachelor_experience: "本科毕业经历"
      bachelor_certificate_number: "本科毕业证书号"
      bachelor_certificate_time: "本科毕业证书时间"
      bachelor_education_certificate: "本科教育证书号"
      bachelor_education_time: "本科教育时间"

      # 学士学位信息
      bachelor_degree_name: "学士学位名称"
      bachelor_degree_award_time: "学士学位授予时间"
      bachelor_language_certificate: "本科语言证书号"
      bachelor_language_time: "本科语言时间"

      # 其他标识
      is_high_level_talent: "是否高层次人才"
      college_first_choice_status: "院校第一志愿状态"
      is_rural_student: "是否农村学生"
      college_other_info: "学院其他信息"
      degree_certificate_number2: "学位证书号2"
      study_duration: "学生学制"
      student_training_level_code: "学生培养层次码"
      is_minor: "是否辅修"
      registration_category_code: "学籍类别码"
      is_university_training: "是否大学培养"
      university_training_unit: "大学培养单位"

      # 系统字段
      created_at: "记录创建时间"
      updated_at: "记录更新时间"

    sync_interval: 3600  # 每小时同步一次
    enabled: true


# 全局配置
global:
  # 日志级别: debug, info, warn, error
  log_level: "info"
  # 日志文件路径
  log_file: "data_sync.log"
  # 同步模式: replace（替换）, append（追加）, upsert（更新插入）
  sync_mode: "upsert"  # 推荐使用upsert模式
  # 错误重试次数
  retry_count: 5
  # 请求超时时间（秒）
  request_timeout: 60
