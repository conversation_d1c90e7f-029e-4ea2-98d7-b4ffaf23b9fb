# 科研项目表配置说明

## 📋 配置概述

新增了科研项目表 (research_project) 的API配置，用于同步学生课程成绩和科研项目相关信息。

### API信息
- **API名称**: `research_project`
- **描述**: 科研项目表同步
- **接口地址**: `http://10.89.10.81:33024/1033769420954/api/ky/fdm_kygl_kjxm`
- **请求方法**: GET
- **认证方式**: X-H3C-ID + X-H3C-APPKEY
- **分页支持**: 是 (pageSize + pageNum)

## 🗂️ 字段映射详情 (共80+个字段)

### 基本标识信息
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| id | record_id | VARCHAR(50) PRIMARY KEY | 记录ID |
| xh | student_number | VARCHAR(20) | 学号 |
| xm | student_name | VARCHAR(50) | 学生姓名 |

### 课程基本信息
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| kcbid | course_id | VARCHAR(20) | 课程表ID |
| kcbh | course_code | VARCHAR(20) | 课程编号 |
| kcmc | course_name | VARCHAR(100) | 课程名称 |
| xnxqid | semester_id | VARCHAR(20) | 学年学期ID |
| xqmc | semester_name | VARCHAR(20) | 学期名称 |

### 考试信息
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| ksxzm | exam_type_code | VARCHAR(10) | 考试性质码 |
| ksxzmc | exam_type_name | VARCHAR(30) | 考试性质名称 |
| khfsm | exam_method_code | VARCHAR(10) | 考核方式码 |
| khfsmc | exam_method_name | VARCHAR(30) | 考核方式名称 |

### 开课单位信息
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| kkbmbh | teaching_dept_code | VARCHAR(20) | 开课部门编号 |
| kkbm | teaching_dept_name | VARCHAR(100) | 开课部门名称 |

### 成绩信息
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| zcj | total_score | DECIMAL(5,2) | 总成绩 |
| zxs | total_hours | DECIMAL(6,2) | 总学时 |
| xf | credit_points | DECIMAL(4,2) | 学分 |
| cjfs | score_method | VARCHAR(10) | 成绩方式 |
| cjfsmc | score_method_name | VARCHAR(30) | 成绩方式名称 |
| cjxs | score_display | VARCHAR(20) | 成绩显示 |
| dzcj | final_score | DECIMAL(5,2) | 最终成绩 |

### 课程属性
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| kclbm | course_category_code | VARCHAR(10) | 课程类别码 |
| kclbmc | course_category_name | VARCHAR(30) | 课程类别名称 |
| kcxzm | course_nature_code | VARCHAR(10) | 课程性质码 |
| kcxzmc | course_nature_name | VARCHAR(30) | 课程性质名称 |
| txklbm | course_type_code | VARCHAR(10) | 通选课类别码 |
| txklbmc | course_type_name | VARCHAR(50) | 通选课类别名称 |

### 教师信息
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| jsxm | teacher_name | VARCHAR(50) | 教师姓名 |

### 成绩标识
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| cjbs | score_flag | VARCHAR(20) | 成绩标识 |
| cjbsmz | score_flag_name | VARCHAR(50) | 成绩标识名称 |
| sffxkc | is_elective | VARCHAR(5) | 是否辅修课程 |
| sfsxwkc | is_minor_course | VARCHAR(5) | 是否辅修学位课程 |
| sfxwk | is_degree_course | VARCHAR(5) | 是否学位课 |
| bxbj | is_required_class | VARCHAR(5) | 必修班级 |

### 操作信息
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| cjr | score_recorder | VARCHAR(50) | 成绩录入人 |
| cjsj | score_record_time | DATETIME | 成绩录入时间 |
| xgr | modifier | VARCHAR(50) | 修改人 |
| xgsj | modify_time | DATETIME | 修改时间 |
| jlsfsc | is_record_deleted | VARCHAR(5) | 记录是否删除 |
| bz | remarks | TEXT | 备注 |

### 补考信息
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| bcxq | makeup_semester | VARCHAR(20) | 补考学期 |
| bcxqmc | makeup_semester_name | VARCHAR(20) | 补考学期名称 |

### 学院专业班级信息
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| yxdm | college_code | VARCHAR(20) | 学院代码 |
| yxmc | college_name | VARCHAR(100) | 学院名称 |
| zybh | major_code | VARCHAR(20) | 专业编号 |
| zymc | major_name | VARCHAR(100) | 专业名称 |
| bjbh | class_code | VARCHAR(20) | 班级编号 |
| bjmc | class_name | VARCHAR(100) | 班级名称 |

### 学籍信息
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| glbm | management_dept | VARCHAR(50) | 管理部门 |
| xjztm | student_status_code | VARCHAR(10) | 学籍状态码 |
| xjztmc | student_status_name | VARCHAR(30) | 学籍状态名称 |
| dqszj | current_grade | VARCHAR(10) | 当前所在级 |
| rxnf | enrollment_year | VARCHAR(10) | 入学年份 |
| ksxn | exam_year | VARCHAR(10) | 考试学年 |

### 辅修信息
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| fxzydm | minor_major_code | VARCHAR(20) | 辅修专业代码 |
| fxzymc | minor_major_name | VARCHAR(100) | 辅修专业名称 |
| fxbjdm | minor_class_code | VARCHAR(20) | 辅修班级代码 |
| fxbjmc | minor_class_name | VARCHAR(100) | 辅修班级名称 |

### 其他成绩信息
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| cxtdkcid | innovation_course_id | VARCHAR(50) | 创新团队课程ID |
| yszcj | original_total_score | DECIMAL(5,2) | 原始总成绩 |
| yszcjxs | original_score_display | VARCHAR(20) | 原始成绩显示 |
| sm | description | TEXT | 说明 |
| tdbz | special_flag | VARCHAR(20) | 特殊标志 |
| cjdj | score_level | VARCHAR(10) | 成绩等级 |

### 其他标识字段
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| sfjlthkc | is_theory_practice_course | VARCHAR(5) | 是否理论实践课程 |
| tscjbz | special_score_flag | VARCHAR(20) | 特殊成绩标志 |
| sjzr | practice_instructor | VARCHAR(50) | 实践指导人 |
| bysjcjid | graduation_project_score_id | VARCHAR(50) | 毕业设计成绩ID |
| xgxnxq | modify_semester | VARCHAR(20) | 修改学年学期 |
| cjcode | score_code | VARCHAR(20) | 成绩代码 |
| kcxs | course_hours | VARCHAR(20) | 课程学时 |
| sfmf | is_exempted | VARCHAR(5) | 是否免修 |
| jd | grade_point | DECIMAL(4,2) | 绩点 |
| sfjzcj | is_additional_score | VARCHAR(5) | 是否加分成绩 |
| zjf | additional_points | DECIMAL(4,2) | 加分 |
| sfjgcx | is_institution_innovation | VARCHAR(5) | 是否机构创新 |
| sfxfyhdh | is_credit_exchange | VARCHAR(5) | 是否学分互换 |
| sfxfdh | is_credit_substitution | VARCHAR(5) | 是否学分代换 |
| cjzt | score_status | VARCHAR(20) | 成绩状态 |

## 🗄️ 数据库表结构

### 表名: research_project

#### 主要特性
- **主键**: record_id (记录ID)
- **字符集**: utf8mb4
- **存储引擎**: InnoDB
- **字段总数**: 80+个业务字段 + 2个系统字段

#### 索引设计
```sql
-- 主键索引
PRIMARY KEY (record_id)

-- 业务索引
INDEX idx_student (student_number)           -- 学生查询
INDEX idx_course (course_id)                 -- 课程查询
INDEX idx_semester (semester_id)             -- 学期查询
INDEX idx_college_major (college_code, major_code) -- 学院专业查询
INDEX idx_score_record_time (score_record_time)    -- 成绩录入时间查询
INDEX idx_course_category (course_category_code)   -- 课程类别查询
INDEX idx_teacher (teacher_name)             -- 教师查询
INDEX idx_class (class_code)                 -- 班级查询
INDEX idx_exam_year (exam_year)              -- 考试学年查询
```

## ⚙️ 配置特性

### 1. 分页支持
```yaml
params:
  pageSize: "100"  # 每页100条记录
  pageNum: "1"     # 起始页码
```

### 2. 同步频率
- **同步间隔**: 3600秒 (1小时)
- **原因**: 成绩数据相对稳定，1小时同步一次即可

### 3. 字段注释
- 所有字段都有详细的中文注释
- 便于数据库维护和理解

### 4. 数据类型优化
- 成绩字段使用 DECIMAL 类型保证精度
- 时间字段使用 DATETIME 类型
- 文本字段根据内容长度选择合适的 VARCHAR 长度

## 🧪 测试验证

### 测试脚本
- `test_research_config.go` - 验证科研项目配置

### 验证内容
1. **配置完整性**: 检查字段映射、类型定义、注释的一致性
2. **JSON解析**: 测试实际数据的解析和转换
3. **字段分类**: 按功能分类统计字段映射情况
4. **成绩数据**: 特别验证成绩相关字段的处理

### 测试命令
```bash
# 验证配置
go run test_research_config.go

# 编译程序
go build -o api2mysql

# 测试同步
./api2mysql -config config-production.yaml -sync-once research_project
```

## 📊 数据示例

### API响应示例
```json
{
    "id": "FE6F299D238B4EFAB7331F324F0B5711",
    "xh": "00001549",
    "xm": "刘娟娟",
    "kcmc": "计算机基础",
    "zcj": "91.0",
    "xf": "3.0",
    "kclbmc": "必修",
    "yxmc": "航空制造工程学院",
    "zymc": "数控技术（五年制）",
    "cjsj": "2023-11-28 11:31:03"
}
```

### 转换后的数据库记录
```sql
INSERT INTO research_project (
    record_id, student_number, student_name, course_name,
    total_score, credit_points, course_category_name,
    college_name, major_name, score_record_time
) VALUES (
    'FE6F299D238B4EFAB7331F324F0B5711',
    '00001549',
    '刘娟娟',
    '计算机基础',
    91.0,
    3.0,
    '必修',
    '航空制造工程学院',
    '数控技术（五年制）',
    '2023-11-28 11:31:03'
);
```

## 🔍 业务场景

### 适用场景
1. **学生成绩管理**: 维护完整的学生课程成绩信息
2. **学分统计**: 按学生、专业、课程类别统计学分
3. **成绩分析**: 分析学生成绩分布和趋势
4. **教学质量评估**: 按教师、课程分析教学效果
5. **学籍管理**: 跟踪学生学习进度和状态

### 查询示例
```sql
-- 查询某学生的成绩单
SELECT course_name, total_score, credit_points, course_category_name, score_record_time
FROM research_project 
WHERE student_number = '00001549'
ORDER BY score_record_time DESC;

-- 统计某专业的平均成绩
SELECT major_name, AVG(total_score) as avg_score, COUNT(*) as course_count
FROM research_project 
WHERE major_code = '0104' AND total_score IS NOT NULL
GROUP BY major_name;

-- 查询某学期的课程成绩分布
SELECT course_category_name, 
       COUNT(*) as total_count,
       AVG(total_score) as avg_score,
       MIN(total_score) as min_score,
       MAX(total_score) as max_score
FROM research_project 
WHERE semester_id = '2000-2001-1' AND total_score IS NOT NULL
GROUP BY course_category_name;
```

## 📝 维护建议

1. **定期清理**: 定期清理过期学期的成绩数据
2. **索引优化**: 根据查询模式调整索引策略
3. **数据验证**: 定期验证成绩数据的合法性
4. **性能监控**: 监控大数据量查询的性能表现

配置已完成，可以开始测试科研项目数据的同步功能！
