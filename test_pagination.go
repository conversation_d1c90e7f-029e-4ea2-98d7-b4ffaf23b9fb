package main

import (
	"fmt"
	"log"
	"time"

	"github.com/sirupsen/logrus"

	"api2mysql/api"
	"api2mysql/config"
)

func main() {
	// 创建日志器
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel) // 设置为Debug级别以查看详细日志

	// 创建API客户端
	client := api.NewClient(60*time.Second, 3, logger)

	fmt.Println("测试分页功能...")

	// 测试分页API配置
	apiConfig := &config.APIConfig{
		Name:   "test_pagination",
		URL:    "https://jsonplaceholder.typicode.com/posts", // 使用测试API
		Method: "GET",
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
		Params: map[string]string{
			"pageSize": "10", // 每页10条记录
			"pageNum":  "1",  // 起始页码
		},
		FieldMapping: map[string]string{
			"id":     "id",
			"userId": "user_id",
			"title":  "title",
			"body":   "content",
		},
	}

	fmt.Printf("API配置:\n")
	fmt.Printf("  URL: %s\n", apiConfig.URL)
	fmt.Printf("  分页参数: pageSize=%s, pageNum=%s\n", 
		apiConfig.Params["pageSize"], apiConfig.Params["pageNum"])

	// 测试分页数据获取
	fmt.Println("\n开始测试分页数据获取...")
	rawData, err := client.FetchData(apiConfig)
	if err != nil {
		log.Fatalf("获取分页数据失败: %v", err)
	}

	fmt.Printf("✅ 分页获取完成，总共获取 %d 条记录\n", len(rawData))

	// 显示前几条数据
	fmt.Println("\n📋 数据示例:")
	for i, record := range rawData {
		if i >= 3 { // 只显示前3条
			break
		}
		fmt.Printf("记录 %d: %+v\n", i+1, record)
	}

	// 测试数据转换
	fmt.Println("\n🔄 测试数据转换...")
	transformedData, err := client.TransformData(rawData, apiConfig.FieldMapping)
	if err != nil {
		log.Printf("数据转换失败: %v", err)
	} else {
		fmt.Printf("✅ 成功转换 %d 条记录\n", len(transformedData))
		
		// 显示转换后的数据示例
		fmt.Println("\n📋 转换后的数据示例:")
		for i, record := range transformedData {
			if i >= 3 { // 只显示前3条
				break
			}
			fmt.Printf("转换后记录 %d:\n", i+1)
			for key, value := range record {
				fmt.Printf("  %s: %v\n", key, value)
			}
			fmt.Println()
		}
	}

	fmt.Println("🎉 分页功能测试完成！")
}

// 测试不同分页场景的函数
func testDifferentPaginationScenarios() {
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)
	client := api.NewClient(30*time.Second, 3, logger)

	scenarios := []struct {
		name     string
		pageSize string
		desc     string
	}{
		{"small_page", "5", "小页面测试（每页5条）"},
		{"medium_page", "20", "中等页面测试（每页20条）"},
		{"large_page", "50", "大页面测试（每页50条）"},
	}

	for _, scenario := range scenarios {
		fmt.Printf("\n--- %s ---\n", scenario.desc)
		
		apiConfig := &config.APIConfig{
			Name:   scenario.name,
			URL:    "https://jsonplaceholder.typicode.com/posts",
			Method: "GET",
			Headers: map[string]string{
				"Content-Type": "application/json",
			},
			Params: map[string]string{
				"pageSize": scenario.pageSize,
				"pageNum":  "1",
			},
		}

		data, err := client.FetchData(apiConfig)
		if err != nil {
			fmt.Printf("❌ %s 失败: %v\n", scenario.desc, err)
			continue
		}

		fmt.Printf("✅ %s 成功，获取 %d 条记录\n", scenario.desc, len(data))
	}
}

// 测试无分页参数的API
func testNonPaginatedAPI() {
	fmt.Println("\n--- 测试无分页参数的API ---")
	
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)
	client := api.NewClient(30*time.Second, 3, logger)

	// 无分页参数的API配置
	apiConfig := &config.APIConfig{
		Name:   "non_paginated",
		URL:    "https://jsonplaceholder.typicode.com/users",
		Method: "GET",
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
		Params: map[string]string{}, // 无分页参数
	}

	data, err := client.FetchData(apiConfig)
	if err != nil {
		fmt.Printf("❌ 无分页API测试失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 无分页API测试成功，获取 %d 条记录\n", len(data))
}
