package main

import (
	"encoding/json"
	"fmt"
	"log"

	"api2mysql/config"
)

func main() {
	fmt.Println("测试课程数据表配置...")

	// 加载配置文件
	cfg, err := config.LoadConfig("config-production.yaml")
	if err != nil {
		log.Fatalf("加载配置文件失败: %v", err)
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		log.Fatalf("配置验证失败: %v", err)
	}

	// 查找课程数据API配置
	var courseAPI *config.APIConfig
	for _, api := range cfg.APIs {
		if api.Name == "course_schedule" {
			courseAPI = &api
			break
		}
	}

	if courseAPI == nil {
		log.Fatalf("未找到 course_schedule API配置")
	}

	fmt.Printf("✅ 找到课程数据API配置: %s\n", courseAPI.Description)
	fmt.Printf("📊 配置统计:\n")
	fmt.Printf("  - API地址: %s\n", courseAPI.URL)
	fmt.Printf("  - 字段映射数量: %d\n", len(courseAPI.FieldMapping))
	fmt.Printf("  - 字段类型数量: %d\n", len(courseAPI.FieldTypes))
	fmt.Printf("  - 字段注释数量: %d\n", len(courseAPI.FieldComments))
	fmt.Printf("  - 分页参数: pageSize=%s, pageNum=%s\n", 
		courseAPI.Params["pageSize"], courseAPI.Params["pageNum"])

	// 验证字段映射和字段类型的一致性
	fmt.Println("\n🔍 验证字段映射和类型定义的一致性...")
	missingTypes := []string{}
	missingComments := []string{}

	for apiField, dbField := range courseAPI.FieldMapping {
		// 检查字段类型是否存在
		if _, exists := courseAPI.FieldTypes[dbField]; !exists {
			missingTypes = append(missingTypes, fmt.Sprintf("%s -> %s", apiField, dbField))
		}
		
		// 检查字段注释是否存在
		if _, exists := courseAPI.FieldComments[dbField]; !exists {
			missingComments = append(missingComments, fmt.Sprintf("%s -> %s", apiField, dbField))
		}
	}

	if len(missingTypes) > 0 {
		fmt.Printf("❌ 缺少字段类型定义的字段:\n")
		for _, field := range missingTypes {
			fmt.Printf("  - %s\n", field)
		}
	} else {
		fmt.Printf("✅ 所有映射字段都有对应的类型定义\n")
	}

	if len(missingComments) > 0 {
		fmt.Printf("⚠️  缺少字段注释的字段:\n")
		for _, field := range missingComments {
			fmt.Printf("  - %s\n", field)
		}
	} else {
		fmt.Printf("✅ 所有映射字段都有对应的注释\n")
	}

	// 模拟JSON数据测试
	fmt.Println("\n🧪 测试JSON数据解析...")
	testJSON := `{
		"apid": "3B5ACFDEF5474A748DB4492A4460A016",
		"xnxqid": "2006-2007-1",
		"xnxq": "2006-2007-1",
		"kkd": "1",
		"kkdlb": "课表",
		"sjbz": "全部",
		"kkzc": "3-5,7-8,10,13-18,21",
		"kkzcmx": ",3,4,5,7,8,10,13,14,15,16,17,18,21,",
		"kcsjbh": "2GH",
		"kcsj": "20708",
		"kcsjmx": ",207,208,",
		"pklb": "0B02DBC695A64AAA884578F33CEEDBB2",
		"dkbz": "",
		"dkip": "",
		"dkrjsid": "",
		"jsid": "B1842DAF741E44FB8E5B8C3C8A8758D3",
		"jlsfsc": "否",
		"xq": "2",
		"oldjsid": "",
		"cjsj": "2023-03-25 00:00:00",
		"kssj": "16:00",
		"jssj": "17:40",
		"kbjcmsid": "7331D7F8299247C2B91EA9CAAA5A884C",
		"czr": "",
		"czsj": "",
		"pkbj": "",
		"lpyy": "",
		"ljmc": "",
		"ewmmc": "",
		"ewmdz": "",
		"ljlx": "",
		"sftssy": "",
		"syyyid": "",
		"gxsj": "",
		"skkssj": "",
		"skjssj": ""
	}`

	var jsonData map[string]interface{}
	if err := json.Unmarshal([]byte(testJSON), &jsonData); err != nil {
		log.Printf("JSON解析失败: %v", err)
		return
	}

	// 模拟数据转换
	transformedData := make(map[string]interface{})
	mappedFields := 0
	
	for apiField, dbField := range courseAPI.FieldMapping {
		if value, exists := jsonData[apiField]; exists {
			transformedData[dbField] = value
			mappedFields++
		}
	}

	fmt.Printf("✅ 成功映射 %d 个字段\n", mappedFields)
	fmt.Printf("📋 转换后的数据示例:\n")
	
	// 显示部分转换结果
	sampleFields := []string{
		"schedule_id", "semester", "course_location_type", "course_weeks", 
		"course_time_code", "start_time", "end_time", "weekday",
		"teacher_id", "create_time", "is_record_deleted",
	}
	
	for _, field := range sampleFields {
		if value, exists := transformedData[field]; exists {
			comment := courseAPI.FieldComments[field]
			fmt.Printf("  %-25s: %v (%s)\n", field, value, comment)
		}
	}

	// 显示字段分类统计
	fmt.Println("\n📋 字段分类统计:")
	categories := map[string][]string{
		"基本标识": {"schedule_id", "semester_id", "semester"},
		"开课信息": {"course_location_code", "course_location_type", "course_weeks", "time_flag"},
		"时间信息": {"course_time_code", "start_time", "end_time", "weekday"},
		"教师信息": {"teacher_id", "old_teacher_id", "substitute_teacher_id"},
		"排课信息": {"schedule_category", "schedule_class", "course_basic_id"},
		"操作信息": {"operator", "operation_time", "create_time", "update_time"},
	}

	for category, fields := range categories {
		fmt.Printf("\n  %s:\n", category)
		count := 0
		for _, field := range fields {
			if _, exists := transformedData[field]; exists {
				count++
			}
		}
		fmt.Printf("    已映射字段: %d/%d\n", count, len(fields))
	}

	// 分析课程时间数据
	fmt.Println("\n📅 课程时间分析:")
	if semester, ok := transformedData["semester"].(string); ok {
		fmt.Printf("  学年学期: %s\n", semester)
	}
	if weeks, ok := transformedData["course_weeks"].(string); ok {
		fmt.Printf("  开课周次: %s\n", weeks)
	}
	if startTime, ok := transformedData["start_time"].(string); ok {
		fmt.Printf("  开始时间: %s\n", startTime)
	}
	if endTime, ok := transformedData["end_time"].(string); ok {
		fmt.Printf("  结束时间: %s\n", endTime)
	}
	if weekday, ok := transformedData["weekday"].(string); ok {
		fmt.Printf("  星期: %s\n", weekday)
	}

	fmt.Println("\n🎉 课程数据配置验证完成！")
	fmt.Println("\n📝 下一步:")
	fmt.Println("1. 编译程序: go build -o api2mysql")
	fmt.Println("2. 测试同步: ./api2mysql -config config-production.yaml -sync-once course_schedule")
	fmt.Println("3. 检查数据库表结构和数据")
	fmt.Println("4. 验证分页功能是否正常工作")
}
