package main

import (
	"encoding/json"
	"fmt"
	"log"

	"api2mysql/config"
)

func main() {
	fmt.Println("测试科研项目表配置...")

	// 加载配置文件
	cfg, err := config.LoadConfig("config-production.yaml")
	if err != nil {
		log.Fatalf("加载配置文件失败: %v", err)
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		log.Fatalf("配置验证失败: %v", err)
	}

	// 查找科研项目API配置
	var researchAPI *config.APIConfig
	for _, api := range cfg.APIs {
		if api.Name == "research_project" {
			researchAPI = &api
			break
		}
	}

	if researchAPI == nil {
		log.Fatalf("未找到 research_project API配置")
	}

	fmt.Printf("✅ 找到科研项目API配置: %s\n", researchAPI.Description)
	fmt.Printf("📊 配置统计:\n")
	fmt.Printf("  - API地址: %s\n", researchAPI.URL)
	fmt.Printf("  - 字段映射数量: %d\n", len(researchAPI.FieldMapping))
	fmt.Printf("  - 字段类型数量: %d\n", len(researchAPI.FieldTypes))
	fmt.Printf("  - 字段注释数量: %d\n", len(researchAPI.FieldComments))
	fmt.Printf("  - 分页参数: pageSize=%s, pageNum=%s\n", 
		researchAPI.Params["pageSize"], researchAPI.Params["pageNum"])

	// 验证字段映射和字段类型的一致性
	fmt.Println("\n🔍 验证字段映射和类型定义的一致性...")
	missingTypes := []string{}
	missingComments := []string{}

	for apiField, dbField := range researchAPI.FieldMapping {
		// 检查字段类型是否存在
		if _, exists := researchAPI.FieldTypes[dbField]; !exists {
			missingTypes = append(missingTypes, fmt.Sprintf("%s -> %s", apiField, dbField))
		}
		
		// 检查字段注释是否存在
		if _, exists := researchAPI.FieldComments[dbField]; !exists {
			missingComments = append(missingComments, fmt.Sprintf("%s -> %s", apiField, dbField))
		}
	}

	if len(missingTypes) > 0 {
		fmt.Printf("❌ 缺少字段类型定义的字段:\n")
		for _, field := range missingTypes {
			fmt.Printf("  - %s\n", field)
		}
	} else {
		fmt.Printf("✅ 所有映射字段都有对应的类型定义\n")
	}

	if len(missingComments) > 0 {
		fmt.Printf("⚠️  缺少字段注释的字段:\n")
		for _, field := range missingComments {
			fmt.Printf("  - %s\n", field)
		}
	} else {
		fmt.Printf("✅ 所有映射字段都有对应的注释\n")
	}

	// 模拟JSON数据测试
	fmt.Println("\n🧪 测试JSON数据解析...")
	testJSON := `{
		"wid": "2c29e2f483346e0101833597636a03b2",
		"kydlm": "1",
		"kydlm_mc": "自然科学类",
		"xmbh": "",
		"xmflm": "1",
		"xmflm_mc": "纵向项目",
		"xmmc": "融合视觉信息的机械手分拣姿态分析与自适应抓取方法研究",
		"xmzt": "",
		"xmjj": "",
		"ztc": "",
		"lxrq_jybbgxky0101023": "2022-09-13",
		"xmpzh": "**********",
		"gsdwm": "",
		"gsdwm_mc": "",
		"gsjdptm": "",
		"gsjdptm_mc": "",
		"zxbh": "",
		"ksrq_jybbgxky0101004": "",
		"jxrq": "",
		"xmfzrh": "",
		"xmfzrh_mc": "",
		"xmfzrszdwm": "",
		"xmfzrszdwmc": "",
		"xmwtdw": "",
		"fzrdh": "",
		"fzryx": "",
		"xmlxrzgh": "",
		"xmlxrxm": "",
		"xmlxrdh": "",
		"xmlxryx": "",
		"xmjbm": "126",
		"xmjbm_mc": "自然科学类一般项目",
		"xmlbm": "",
		"xmlbm_mc": "",
		"xmejlbm": "",
		"xmejlbm_mc": "",
		"jhksrq": "2022-09-01",
		"jhwcrq": "2022-12-31"
	}`

	var jsonData map[string]interface{}
	if err := json.Unmarshal([]byte(testJSON), &jsonData); err != nil {
		log.Printf("JSON解析失败: %v", err)
		return
	}

	// 模拟数据转换
	transformedData := make(map[string]interface{})
	mappedFields := 0
	
	for apiField, dbField := range researchAPI.FieldMapping {
		if value, exists := jsonData[apiField]; exists {
			transformedData[dbField] = value
			mappedFields++
		}
	}

	fmt.Printf("✅ 成功映射 %d 个字段\n", mappedFields)
	fmt.Printf("📋 转换后的数据示例:\n")
	
	// 显示部分转换结果
	sampleFields := []string{
		"project_id", "project_name", "research_category_name", "project_type_name", 
		"project_level_name", "project_approval_number", "approval_date", 
		"planned_start_date", "planned_end_date"
	}
	
	for _, field := range sampleFields {
		if value, exists := transformedData[field]; exists {
			comment := researchAPI.FieldComments[field]
			fmt.Printf("  %-25s: %v (%s)\n", field, value, comment)
		}
	}

	// 显示字段分类统计
	fmt.Println("\n📋 字段分类统计:")
	categories := map[string][]string{
		"基本信息": {"project_id", "project_number", "project_name", "project_approval_number"},
		"项目分类": {"research_category_code", "research_category_name", "project_type_code", "project_type_name"},
		"时间信息": {"approval_date", "start_date", "end_date", "planned_start_date", "planned_end_date"},
		"负责人信息": {"project_leader_code", "project_leader_name", "leader_dept_code", "leader_dept_name"},
		"经费信息": {"total_funding", "contract_funding", "platform_funding", "software_funding"},
	}

	for category, fields := range categories {
		fmt.Printf("\n  %s:\n", category)
		count := 0
		for _, field := range fields {
			if _, exists := transformedData[field]; exists {
				count++
			}
		}
		fmt.Printf("    已映射字段: %d/%d\n", count, len(fields))
	}

	// 分析项目信息
	fmt.Println("\n🔬 项目信息分析:")
	if projectName, ok := transformedData["project_name"].(string); ok {
		fmt.Printf("  项目名称: %s\n", projectName)
	}
	if category, ok := transformedData["research_category_name"].(string); ok {
		fmt.Printf("  科研类别: %s\n", category)
	}
	if projectType, ok := transformedData["project_type_name"].(string); ok {
		fmt.Printf("  项目类型: %s\n", projectType)
	}
	if level, ok := transformedData["project_level_name"].(string); ok {
		fmt.Printf("  项目级别: %s\n", level)
	}

	fmt.Println("\n🎉 科研项目配置验证完成！")
	fmt.Println("\n📝 下一步:")
	fmt.Println("1. 编译程序: go build -o api2mysql")
	fmt.Println("2. 测试同步: ./api2mysql -config config-production.yaml -sync-once research_project")
	fmt.Println("3. 检查数据库表结构和数据")
	fmt.Println("4. 验证分页功能是否正常工作")
}
