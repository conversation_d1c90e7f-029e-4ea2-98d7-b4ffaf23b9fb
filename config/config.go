package config

import (
	"fmt"
	"io/ioutil"
	"time"

	"gopkg.in/yaml.v3"
)

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host            string `yaml:"host"`
	Port            int    `yaml:"port"`
	Username        string `yaml:"username"`
	Password        string `yaml:"password"`
	Database        string `yaml:"database"`
	Charset         string `yaml:"charset"`
	MaxOpenConns    int    `yaml:"max_open_conns"`
	MaxIdleConns    int    `yaml:"max_idle_conns"`
	ConnMaxLifetime int    `yaml:"conn_max_lifetime"` // 秒
}

// APIConfig API接口配置
type APIConfig struct {
	Name          string            `yaml:"name"`
	Description   string            `yaml:"description"`
	URL           string            `yaml:"url"`
	Method        string            `yaml:"method"`
	Headers       map[string]string `yaml:"headers"`
	Params        map[string]string `yaml:"params"`
	Body          interface{}       `yaml:"body"`
	TableName     string            `yaml:"table_name"`
	FieldMapping  map[string]string `yaml:"field_mapping"`
	FieldTypes    map[string]string `yaml:"field_types"`
	FieldComments map[string]string `yaml:"field_comments"` // 字段注释
	SyncInterval  int               `yaml:"sync_interval"`  // 秒
	Enabled       bool              `yaml:"enabled"`
}

// GlobalConfig 全局配置
type GlobalConfig struct {
	LogLevel       string `yaml:"log_level"`
	LogFile        string `yaml:"log_file"`
	SyncMode       string `yaml:"sync_mode"`
	RetryCount     int    `yaml:"retry_count"`
	RequestTimeout int    `yaml:"request_timeout"` // 秒
}

// Config 主配置结构
type Config struct {
	Database DatabaseConfig `yaml:"database"`
	APIs     []APIConfig    `yaml:"apis"`
	Global   GlobalConfig   `yaml:"global"`
}

// LoadConfig 从文件加载配置
func LoadConfig(configPath string) (*Config, error) {
	data, err := ioutil.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	var config Config
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 设置默认值
	if config.Global.LogLevel == "" {
		config.Global.LogLevel = "info"
	}
	if config.Global.SyncMode == "" {
		config.Global.SyncMode = "replace"
	}
	if config.Global.RetryCount == 0 {
		config.Global.RetryCount = 3
	}
	if config.Global.RequestTimeout == 0 {
		config.Global.RequestTimeout = 30
	}
	if config.Database.Charset == "" {
		config.Database.Charset = "utf8mb4"
	}
	if config.Database.MaxOpenConns == 0 {
		config.Database.MaxOpenConns = 10
	}
	if config.Database.MaxIdleConns == 0 {
		config.Database.MaxIdleConns = 5
	}
	if config.Database.ConnMaxLifetime == 0 {
		config.Database.ConnMaxLifetime = 300
	}

	return &config, nil
}

// GetDSN 获取数据库连接字符串
func (db *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
		db.Username, db.Password, db.Host, db.Port, db.Database, db.Charset)
}

// GetSyncInterval 获取同步间隔时间
func (api *APIConfig) GetSyncInterval() time.Duration {
	if api.SyncInterval <= 0 {
		return 5 * time.Minute // 默认5分钟
	}
	return time.Duration(api.SyncInterval) * time.Second
}

// GetRequestTimeout 获取请求超时时间
func (g *GlobalConfig) GetRequestTimeout() time.Duration {
	return time.Duration(g.RequestTimeout) * time.Second
}

// Validate 验证配置
func (c *Config) Validate() error {
	// 验证数据库配置
	if c.Database.Host == "" {
		return fmt.Errorf("数据库主机地址不能为空")
	}
	if c.Database.Username == "" {
		return fmt.Errorf("数据库用户名不能为空")
	}
	if c.Database.Database == "" {
		return fmt.Errorf("数据库名不能为空")
	}

	// 验证API配置
	if len(c.APIs) == 0 {
		return fmt.Errorf("至少需要配置一个API接口")
	}

	for i, api := range c.APIs {
		if api.Name == "" {
			return fmt.Errorf("第%d个API的名称不能为空", i+1)
		}
		if api.URL == "" {
			return fmt.Errorf("API '%s' 的URL不能为空", api.Name)
		}
		if api.TableName == "" {
			return fmt.Errorf("API '%s' 的表名不能为空", api.Name)
		}
		if len(api.FieldMapping) == 0 {
			return fmt.Errorf("API '%s' 的字段映射不能为空", api.Name)
		}
		if len(api.FieldTypes) == 0 {
			return fmt.Errorf("API '%s' 的字段类型定义不能为空", api.Name)
		}

		// 验证字段映射和字段类型的一致性
		for _, dbField := range api.FieldMapping {
			if _, exists := api.FieldTypes[dbField]; !exists {
				return fmt.Errorf("API '%s' 的字段映射中的数据库字段 '%s' 在字段类型定义中不存在", api.Name, dbField)
			}
		}
	}

	return nil
}
