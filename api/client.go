package api

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/sirupsen/logrus"

	"api2mysql/config"
)

// Client API客户端
type Client struct {
	httpClient *http.Client
	logger     *logrus.Logger
	retryCount int
}

// NewClient 创建API客户端
func NewClient(timeout time.Duration, retryCount int, logger *logrus.Logger) *Client {
	return &Client{
		httpClient: &http.Client{
			Timeout: timeout,
		},
		logger:     logger,
		retryCount: retryCount,
	}
}

// FetchData 从API获取数据
func (c *Client) FetchData(apiConfig *config.APIConfig) ([]interface{}, error) {
	c.logger.Infof("开始获取API数据: %s", apiConfig.Name)

	// 检查是否需要分页
	if c.needsPagination(apiConfig) {
		return c.fetchPaginatedData(apiConfig)
	}

	// 非分页请求的原有逻辑
	return c.fetchSingleRequest(apiConfig)
}

// needsPagination 检查是否需要分页
func (c *Client) needsPagination(apiConfig *config.APIConfig) bool {
	// 检查参数中是否包含分页参数
	_, hasPageSize := apiConfig.Params["pageSize"]
	_, hasPageNum := apiConfig.Params["pageNum"]
	return hasPageSize || hasPageNum
}

// fetchPaginatedData 获取分页数据
func (c *Client) fetchPaginatedData(apiConfig *config.APIConfig) ([]interface{}, error) {
	var allData []interface{}
	pageNum := 1
	pageSize := 100 // 默认每页100条

	// 从配置中获取页面大小
	if pageSizeStr, exists := apiConfig.Params["pageSize"]; exists {
		if size, err := strconv.Atoi(pageSizeStr); err == nil && size > 0 {
			pageSize = size
		}
	}

	c.logger.Infof("开始分页获取数据，每页 %d 条记录", pageSize)

	for {
		c.logger.Debugf("获取第 %d 页数据", pageNum)

		// 创建当前页的API配置副本
		currentPageConfig := *apiConfig
		currentPageConfig.Params = make(map[string]string)

		// 复制原有参数
		for k, v := range apiConfig.Params {
			currentPageConfig.Params[k] = v
		}

		// 设置当前页参数
		currentPageConfig.Params["pageNum"] = strconv.Itoa(pageNum)
		currentPageConfig.Params["pageSize"] = strconv.Itoa(pageSize)

		// 获取当前页数据
		pageData, err := c.fetchSingleRequest(&currentPageConfig)
		if err != nil {
			return nil, fmt.Errorf("获取第 %d 页数据失败: %v", pageNum, err)
		}

		// 如果当前页没有数据，说明已经到最后一页
		if len(pageData) == 0 {
			c.logger.Infof("第 %d 页无数据，分页获取完成", pageNum)
			break
		}

		// 添加到总数据中
		allData = append(allData, pageData...)
		c.logger.Infof("第 %d 页获取到 %d 条记录，累计 %d 条记录", pageNum, len(pageData), len(allData))

		// 如果当前页数据少于页面大小，说明是最后一页
		if len(pageData) < pageSize {
			c.logger.Infof("第 %d 页数据不足 %d 条，分页获取完成", pageNum, pageSize)
			break
		}

		pageNum++

		// 添加页面间延迟，避免请求过于频繁
		time.Sleep(100 * time.Millisecond)
	}

	c.logger.Infof("分页获取完成，总共获取 %d 条记录，共 %d 页", len(allData), pageNum)
	return allData, nil
}

// fetchSingleRequest 获取单个请求的数据（原有逻辑）
func (c *Client) fetchSingleRequest(apiConfig *config.APIConfig) ([]interface{}, error) {
	var response []interface{}
	var lastErr error

	// 重试机制
	for attempt := 0; attempt <= c.retryCount; attempt++ {
		if attempt > 0 {
			c.logger.Warnf("第 %d 次重试获取API数据: %s", attempt, apiConfig.Name)
			time.Sleep(time.Duration(attempt) * time.Second) // 递增延迟
		}

		data, err := c.makeRequest(apiConfig)
		if err != nil {
			lastErr = err
			c.logger.Errorf("获取API数据失败 (尝试 %d/%d): %v", attempt+1, c.retryCount+1, err)
			continue
		}

		// 解析响应数据
		response, err = c.parseResponse(data)
		if err != nil {
			lastErr = err
			c.logger.Errorf("解析API响应失败 (尝试 %d/%d): %v", attempt+1, c.retryCount+1, err)
			continue
		}

		c.logger.Infof("成功获取API数据: %s，共 %d 条记录", apiConfig.Name, len(response))
		return response, nil
	}

	return nil, fmt.Errorf("获取API数据失败，已重试 %d 次: %v", c.retryCount, lastErr)
}

// makeRequest 发起HTTP请求
func (c *Client) makeRequest(apiConfig *config.APIConfig) ([]byte, error) {
	// 构建URL
	requestURL := apiConfig.URL
	if len(apiConfig.Params) > 0 {
		params := url.Values{}
		for key, value := range apiConfig.Params {
			params.Add(key, value)
		}
		if strings.Contains(requestURL, "?") {
			requestURL += "&" + params.Encode()
		} else {
			requestURL += "?" + params.Encode()
		}
	}

	// 准备请求体
	var requestBody []byte
	var err error
	if apiConfig.Body != nil && (apiConfig.Method == "POST" || apiConfig.Method == "PUT" || apiConfig.Method == "PATCH") {
		requestBody, err = json.Marshal(apiConfig.Body)
		if err != nil {
			return nil, fmt.Errorf("序列化请求体失败: %v", err)
		}
	}

	// 创建请求
	req, err := http.NewRequest(apiConfig.Method, requestURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	for key, value := range apiConfig.Headers {
		req.Header.Set(key, value)
	}

	// 如果有请求体，确保设置Content-Type
	if len(requestBody) > 0 && req.Header.Get("Content-Type") == "" {
		req.Header.Set("Content-Type", "application/json")
	}

	c.logger.Debugf("发起HTTP请求: %s %s", apiConfig.Method, requestURL)

	// 发起请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return nil, fmt.Errorf("HTTP请求失败，状态码: %d，响应: %s", resp.StatusCode, string(body))
	}

	return body, nil
}

// parseResponse 解析API响应
func (c *Client) parseResponse(data []byte) ([]interface{}, error) {
	var result interface{}
	err := json.Unmarshal(data, &result)
	if err != nil {
		return nil, fmt.Errorf("JSON解析失败: %v", err)
	}

	// 处理不同类型的响应
	switch v := result.(type) {
	case []interface{}:
		// 直接是数组
		return v, nil
	case map[string]interface{}:
		// 是对象，可能包含数据数组
		// 尝试常见的数据字段名
		dataFields := []string{"data", "items", "results", "list", "records"}
		for _, field := range dataFields {
			if data, exists := v[field]; exists {
				if dataArray, ok := data.([]interface{}); ok {
					return dataArray, nil
				}
			}
		}
		// 如果没找到数组字段，将对象本身作为单条记录
		return []interface{}{v}, nil
	default:
		return nil, fmt.Errorf("不支持的响应格式，期望数组或对象，得到: %T", result)
	}
}

// TransformData 转换数据格式
func (c *Client) TransformData(rawData []interface{}, fieldMapping map[string]string) ([]map[string]interface{}, error) {
	var transformedData []map[string]interface{}

	for i, item := range rawData {
		itemMap, ok := item.(map[string]interface{})
		if !ok {
			c.logger.Warnf("跳过第 %d 条记录，不是有效的对象格式", i+1)
			continue
		}

		transformedItem := make(map[string]interface{})

		// 根据字段映射转换数据
		for apiField, dbField := range fieldMapping {
			value := c.getNestedValue(itemMap, apiField)
			transformedItem[dbField] = value
		}

		transformedData = append(transformedData, transformedItem)
	}

	c.logger.Infof("数据转换完成，共处理 %d 条记录", len(transformedData))
	return transformedData, nil
}

// getNestedValue 获取嵌套字段的值
func (c *Client) getNestedValue(data map[string]interface{}, fieldPath string) interface{} {
	// 支持点号分隔的嵌套字段，如 "address.street"
	parts := strings.Split(fieldPath, ".")
	current := data

	for i, part := range parts {
		if current == nil {
			return nil
		}

		value, exists := current[part]
		if !exists {
			return nil
		}

		// 如果是最后一个部分，返回值
		if i == len(parts)-1 {
			return value
		}

		// 否则继续向下查找
		if nextMap, ok := value.(map[string]interface{}); ok {
			current = nextMap
		} else {
			return nil
		}
	}

	return nil
}
