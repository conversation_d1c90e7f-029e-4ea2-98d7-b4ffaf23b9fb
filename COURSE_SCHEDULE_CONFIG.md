# 课程数据表配置说明

## 📋 配置概述

新增了课程数据表 (course_schedule) 的API配置，用于同步课程排课信息。

### API信息
- **API名称**: `course_schedule`
- **描述**: 课程数据表同步
- **接口地址**: `http://10.89.10.81:33024/api/v1/fdm/gxjx/fdm_gxjx_kbsj`
- **请求方法**: GET
- **认证方式**: X-H3C-ID + X-H3C-APPKEY
- **分页支持**: 是 (pageSize + pageNum)

## 🗂️ 字段映射详情

### 基本标识信息
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| apid | schedule_id | VARCHAR(50) PRIMARY KEY | 排课ID |
| xnxqid | semester_id | VARCHAR(20) | 学年学期ID |
| xnxq | semester | VARCHAR(20) | 学年学期 |

### 开课信息
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| kkd | course_location_code | VARCHAR(10) | 开课地点代码 |
| kkdlb | course_location_type | VARCHAR(20) | 开课地点类别 |
| sjbz | time_flag | VARCHAR(20) | 时间标志 |
| kkzc | course_weeks | VARCHAR(100) | 开课周次 |
| kkzcmx | course_weeks_detail | VARCHAR(200) | 开课周次明细 |

### 课程时间信息
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| kcsjbh | course_time_code | VARCHAR(20) | 课程时间编号 |
| kcsj | course_time | VARCHAR(20) | 课程时间 |
| kcsjmx | course_time_detail | VARCHAR(100) | 课程时间明细 |
| kssj | start_time | TIME | 开始时间 |
| jssj | end_time | TIME | 结束时间 |
| xq | weekday | VARCHAR(5) | 星期 |

### 排课信息
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| pklb | schedule_category | VARCHAR(50) | 排课类别 |
| dkbz | substitute_flag | VARCHAR(10) | 代课标志 |
| dkip | substitute_ip | VARCHAR(50) | 代课IP |
| dkrjsid | substitute_teacher_id | VARCHAR(50) | 代课教师ID |
| pkbj | schedule_class | VARCHAR(100) | 排课班级 |

### 教师信息
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| jsid | teacher_id | VARCHAR(50) | 教师ID |
| oldjsid | old_teacher_id | VARCHAR(50) | 原教师ID |
| jlsfsc | is_record_deleted | VARCHAR(5) | 记录是否删除 |

### 课表基础信息
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| kbjcmsid | course_basic_id | VARCHAR(50) | 课表基础描述ID |

### 操作信息
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| czr | operator | VARCHAR(50) | 操作人 |
| czsj | operation_time | DATETIME | 操作时间 |
| cjsj | create_time | DATETIME | 创建时间 |
| gxsj | update_time | DATETIME | 更新时间 |

### 录播相关
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| lpyy | recording_reason | VARCHAR(100) | 录播原因 |
| ljmc | link_name | VARCHAR(100) | 链接名称 |
| ewmmc | qr_code_name | VARCHAR(100) | 二维码名称 |
| ewmdz | qr_code_address | VARCHAR(200) | 二维码地址 |
| ljlx | link_type | VARCHAR(20) | 链接类型 |

### 实验室相关
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| sftssy | is_special_lab | VARCHAR(5) | 是否特殊实验室 |
| syyyid | lab_reason_id | VARCHAR(50) | 实验室原因ID |

### 上课时间
| API字段 | 数据库字段 | 类型 | 说明 |
|---------|------------|------|------|
| skkssj | actual_start_time | TIME | 实际开始时间 |
| skjssj | actual_end_time | TIME | 实际结束时间 |

## 🗄️ 数据库表结构

### 表名: course_schedule

#### 主要特性
- **主键**: schedule_id (排课ID)
- **字符集**: utf8mb4
- **存储引擎**: InnoDB
- **字段总数**: 35个业务字段 + 2个系统字段

#### 索引设计
```sql
-- 主键索引
PRIMARY KEY (schedule_id)

-- 业务索引
INDEX idx_semester (semester_id)           -- 学期查询
INDEX idx_teacher (teacher_id)             -- 教师查询
INDEX idx_course_basic (course_basic_id)   -- 课程基础信息查询
INDEX idx_weekday_time (weekday, start_time) -- 时间查询
INDEX idx_course_weeks (course_weeks)      -- 周次查询
INDEX idx_create_time (create_time)        -- 创建时间查询
INDEX idx_schedule_category (schedule_category) -- 排课类别查询
```

## ⚙️ 配置特性

### 1. 分页支持
```yaml
params:
  pageSize: "100"  # 每页100条记录
  pageNum: "1"     # 起始页码
```

### 2. 同步频率
- **同步间隔**: 1800秒 (30分钟)
- **原因**: 课程数据变化较频繁，需要及时同步

### 3. 字段注释
- 所有字段都有详细的中文注释
- 便于数据库维护和理解

### 4. 数据类型优化
- 时间字段使用 TIME 类型
- 日期时间字段使用 DATETIME 类型
- 文本字段根据内容长度选择合适的 VARCHAR 长度

## 🧪 测试验证

### 测试脚本
- `test_course_config.go` - 验证课程数据配置

### 验证内容
1. **配置完整性**: 检查字段映射、类型定义、注释的一致性
2. **JSON解析**: 测试实际数据的解析和转换
3. **字段分类**: 按功能分类统计字段映射情况
4. **时间数据**: 特别验证时间相关字段的处理

### 测试命令
```bash
# 验证配置
go run test_course_config.go

# 编译程序
go build -o api2mysql

# 测试同步
./api2mysql -config config-production.yaml -sync-once course_schedule
```

## 📊 数据示例

### API响应示例
```json
{
    "apid": "3B5ACFDEF5474A748DB4492A4460A016",
    "xnxqid": "2006-2007-1",
    "xnxq": "2006-2007-1",
    "kkdlb": "课表",
    "kkzc": "3-5,7-8,10,13-18,21",
    "kssj": "16:00",
    "jssj": "17:40",
    "xq": "2",
    "jsid": "B1842DAF741E44FB8E5B8C3C8A8758D3",
    "cjsj": "2023-03-25 00:00:00"
}
```

### 转换后的数据库记录
```sql
INSERT INTO course_schedule (
    schedule_id, semester_id, semester, course_location_type,
    course_weeks, start_time, end_time, weekday, teacher_id, create_time
) VALUES (
    '3B5ACFDEF5474A748DB4492A4460A016',
    '2006-2007-1',
    '2006-2007-1', 
    '课表',
    '3-5,7-8,10,13-18,21',
    '16:00',
    '17:40',
    '2',
    'B1842DAF741E44FB8E5B8C3C8A8758D3',
    '2023-03-25 00:00:00'
);
```

## 🔍 业务场景

### 适用场景
1. **课程表管理**: 维护完整的课程排课信息
2. **教师课表查询**: 根据教师ID查询授课安排
3. **教室使用统计**: 分析教室使用情况
4. **时间冲突检测**: 检查排课时间冲突
5. **学期课程统计**: 按学期统计课程数据

### 查询示例
```sql
-- 查询某教师的课程安排
SELECT semester, course_weeks, weekday, start_time, end_time, schedule_class
FROM course_schedule 
WHERE teacher_id = 'B1842DAF741E44FB8E5B8C3C8A8758D3'
ORDER BY weekday, start_time;

-- 查询某学期的课程统计
SELECT semester, COUNT(*) as course_count, 
       COUNT(DISTINCT teacher_id) as teacher_count
FROM course_schedule 
WHERE semester_id = '2006-2007-1'
GROUP BY semester;

-- 查询周二下午的课程安排
SELECT schedule_id, teacher_id, start_time, end_time, schedule_class
FROM course_schedule 
WHERE weekday = '2' AND start_time >= '14:00'
ORDER BY start_time;
```

## 📝 维护建议

1. **定期清理**: 定期清理过期学期的课程数据
2. **索引优化**: 根据查询模式调整索引策略
3. **数据验证**: 定期验证时间字段的合法性
4. **性能监控**: 监控分页查询的性能表现

配置已完成，可以开始测试课程数据的同步功能！
