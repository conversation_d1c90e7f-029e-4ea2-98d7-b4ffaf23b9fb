# 字段映射更新说明

## 📋 更新概述

根据提供的实际JSON数据结构，已更新了以下API配置的字段映射：

1. **student_basic** - 学生基本信息
2. **teacher_basic** - 教师基本信息

## 🎓 学生基本信息 (student_basic) 更新

### 新增字段映射 (共60+个字段)

#### 基本信息
- `xh` → `student_number` (学号)
- `xm` → `student_name` (姓名)
- `ywxm` → `english_name` (英文姓名)
- `xmpy` → `name_pinyin` (姓名拼音)
- `xjh` → `student_id_number` (学籍号)

#### 个人信息
- `xbm` → `gender_code` (性别码)
- `xb` → `gender` (性别)
- `csrq` → `birth_date` (出生日期)
- `mzm` → `ethnicity_code` (民族码)
- `mz` → `ethnicity` (民族)
- `sfzjlxm` → `id_type_code` (身份证件类型码)
- `sfzjlx` → `id_type` (身份证件类型)
- `sfzh` → `id_number` (身份证号)

#### 学院专业信息
- `yxdm` → `college_code` (学院代码)
- `yxmc` → `college_name` (学院名称)
- `zydm` → `major_code` (专业代码)
- `zymc` → `major_name` (专业名称)
- `bjdm` → `class_code` (班级代码)
- `bjmc` → `class_name` (班级名称)

#### 学生状态
- `xsdqztm` → `student_status_code` (学生当前状态码)
- `xsdqzt` → `student_status` (学生当前状态)
- `rxztm` → `enrollment_status_code` (入学状态码)
- `rxzt` → `enrollment_status` (入学状态)

#### 培养信息
- `pyccm` → `education_level_code` (培养层次码)
- `pycc` → `education_level` (培养层次)
- `rxnf` → `enrollment_year` (入学年份)
- `xznj` → `current_grade` (现在年级)

#### 政治面貌和健康
- `zzmmm` → `political_status_code` (政治面貌码)
- `zzmm` → `political_status` (政治面貌)
- `jkzkm` → `health_status_code` (健康状况码)
- `jkzk` → `health_status` (健康状况)

#### 联系方式
- `dzxx` → `email` (电子邮箱)
- `txdz` → `contact_address` (通讯地址)
- `yzbm` → `postal_code` (邮政编码)
- `jstxh` → `mobile_phone` (手机号)
- `qqh` → `qq_number` (QQ号)
- `wxh` → `wechat_number` (微信号)

#### 家庭信息
- `jtzz` → `family_address` (家庭住址)
- `fqxm` → `father_name` (父亲姓名)
- `fqzw` → `father_occupation` (父亲职务)
- `mqxm` → `mother_name` (母亲姓名)
- `mqzw` → `mother_occupation` (母亲职务)

#### 录取信息
- `rxcj` → `admission_score` (入学成绩)
- `lqpcm` → `admission_batch_code` (录取批次码)
- `lqpc` → `admission_batch` (录取批次)
- `lqlbm` → `admission_category_code` (录取类别码)
- `lqlb` → `admission_category` (录取类别)

## 👨‍🏫 教师基本信息 (teacher_basic) 更新

### 新增字段映射 (共50+个字段)

#### 基本信息
- `gh` → `teacher_number` (工号)
- `xm` → `teacher_name` (姓名)
- `zjhm` → `id_number` (证件号码)
- `xbm` → `gender_code` (性别码)
- `xb` → `gender` (性别)
- `xbmc` → `gender_name` (性别名称)
- `csrq` → `birth_date` (出生日期)

#### 国籍民族信息
- `gjm` → `country_code` (国家码)
- `gj` → `country` (国家)
- `gjmc` → `country_name` (国家名称)
- `mzm` → `ethnicity_code` (民族码)
- `mz` → `ethnicity` (民族)
- `mzmc` → `ethnicity_name` (民族名称)
- `jgm` → `origin_code` (籍贯码)
- `jg` → `origin` (籍贯)

#### 证件信息
- `zjlxm` → `id_type_code` (证件类型码)
- `zjlxmc` → `id_type_name` (证件类型名称)

#### 政治面貌
- `zzmmm` → `political_status_code` (政治面貌码)
- `zzmmmc` → `political_status_name` (政治面貌名称)
- `hyzkm` → `marital_status_code` (婚姻状况码)
- `hyzkmc` → `marital_status_name` (婚姻状况名称)

#### 工作信息
- `prsj` → `appointment_date` (聘任时间)
- `rzrq` → `entry_date` (入职日期)
- `dwbm` → `department_code` (单位编码)
- `dwmc` → `department_name` (单位名称)
- `gwlb` → `position_category` (岗位类别)
- `ydlx` → `mobility_type` (异动类型)
- `sfzb` → `is_on_duty` (是否在编)
- `sfssxjs` → `is_part_time_teacher` (是否双师型教师)
- `xzzw` → `administrative_position` (行政职务)
- `zyjszw` → `professional_title_position` (专业技术职务)
- `zc` → `professional_title` (职称)

#### 学历学位信息
- `zgxl` → `highest_education` (最高学历)
- `zgxw` → `highest_degree` (最高学位)
- `byyx` → `graduate_school` (毕业院校)
- `sxzy` → `major` (所学专业)

#### 教职工状态
- `jzglbm` → `staff_category_code` (教职工类别码)
- `jzglbmc` → `staff_category_name` (教职工类别名称)
- `jzgdqztm` → `current_status_code` (教职工当前状态码)
- `jzgdqztmc` → `current_status_name` (教职工当前状态名称)
- `jzgdqzt` → `current_status` (教职工当前状态)
- `jzglb` → `staff_category` (教职工类别)
- `jzgly` → `staff_source` (教职工来源)

#### 其他信息
- `qdhtqk` → `contract_situation` (签订合同情况)
- `lxdh` → `contact_phone` (联系电话)
- `dzyx` → `email` (电子邮箱)
- `zp` → `photo` (照片)
- `sklx` → `subject_category` (学科类型)
- `sfwbkssk` → `is_external_lecturer` (是否外聘课时授课)
- `xxjylx` → `continuing_education_type` (继续教育类型)
- `bxnsfsk` → `is_part_time_teaching` (本校内是否授课)
- `bskyy` → `no_teaching_reason` (不授课原因)
- `jxly` → `teaching_experience` (教学履历)
- `sffzsszdk` → `is_vocational_skills` (是否职业技能)

## 🔧 配置特性

### 1. 完整的字段映射
- 每个API字段都有对应的数据库字段名
- 使用英文命名规范，便于理解和维护

### 2. 详细的字段注释
- 每个数据库字段都有中文注释
- 创建表时会自动添加字段注释

### 3. 合适的字段类型
- 根据数据特点选择合适的MySQL字段类型
- 考虑了数据长度和精度要求

### 4. 分页支持
- 两个API都配置了分页参数
- 支持自动循环获取所有页面数据

## 🧪 测试验证

### 测试脚本
- `test_teacher_config.go` - 验证教师配置
- `test_updated_student_config.go` - 验证学生配置

### 验证内容
1. 配置文件语法正确性
2. 字段映射完整性
3. 字段类型定义一致性
4. 字段注释完整性
5. JSON数据转换测试

## 📝 使用方法

### 1. 验证配置
```bash
# 验证教师配置
go run test_teacher_config.go

# 验证学生配置  
go run test_updated_student_config.go
```

### 2. 测试同步
```bash
# 编译程序
go build -o api2mysql

# 测试学生数据同步
./api2mysql -config config-production.yaml -sync-once student_basic

# 测试教师数据同步
./api2mysql -config config-production.yaml -sync-once teacher_basic
```

### 3. 查看数据库表结构
```sql
-- 查看学生表结构
SHOW CREATE TABLE student_basic;
DESCRIBE student_basic;

-- 查看教师表结构
SHOW CREATE TABLE teacher_basic;
DESCRIBE teacher_basic;
```

## 🎯 配置优势

1. **完整性**：覆盖了JSON中的所有字段
2. **规范性**：使用统一的英文命名规范
3. **可读性**：详细的中文注释说明
4. **扩展性**：易于添加新字段或修改现有字段
5. **维护性**：结构化的配置便于维护和更新

## 📊 数据库表预览

### student_basic 表
- 主键：`student_number` (学号)
- 约60个字段，涵盖学生的完整信息
- 包含基本信息、学院专业、联系方式、家庭信息等

### teacher_basic 表  
- 主键：`teacher_number` (工号)
- 约50个字段，涵盖教师的完整信息
- 包含基本信息、工作信息、学历信息、状态信息等

配置更新完成，可以开始测试数据同步功能！
