-- H3C数据同步数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS h3c_data_sync 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE h3c_data_sync;

-- 创建学生基本数据表
CREATE TABLE IF NOT EXISTS fdm_gxxs_xsjbsj (
    student_number VARCHAR(20) PRIMARY KEY COMMENT '学号',
    student_name VARCHAR(50) NOT NULL COMMENT '姓名',
    english_name VARCHAR(100) COMMENT '英文姓名',
    name_pinyin VARCHAR(100) COMMENT '姓名拼音',
    college_code VARCHAR(20) COMMENT '学院代码',
    college_name VARCHAR(100) COMMENT '学院名称',
    major_code VARCHAR(20) COMMENT '专业代码',
    major_name VARCHAR(100) COMMENT '专业名称',
    class_code VARCHAR(20) COMMENT '班级代码',
    class_name VARCHAR(100) COMMENT '班级名称',
    student_status_code VARCHAR(10) COMMENT '学生状态码',
    student_status VARCHAR(20) COMMENT '学生状态',
    email VARCHAR(100) COMMENT '电子邮箱',
    student_id_number VARCHAR(30) COMMENT '学籍号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_college_code (college_code),
    INDEX idx_major_code (major_code),
    INDEX idx_class_code (class_code),
    INDEX idx_student_status (student_status_code),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生基本数据子类表';

-- 创建数据同步日志表
CREATE TABLE IF NOT EXISTS sync_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    api_name VARCHAR(50) NOT NULL COMMENT 'API名称',
    table_name VARCHAR(50) NOT NULL COMMENT '表名',
    sync_mode VARCHAR(20) NOT NULL COMMENT '同步模式',
    records_count INT DEFAULT 0 COMMENT '同步记录数',
    status VARCHAR(20) NOT NULL COMMENT '同步状态: success, failed',
    error_message TEXT COMMENT '错误信息',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    duration_ms INT DEFAULT 0 COMMENT '耗时(毫秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_api_name (api_name),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据同步日志表';

-- 创建API配置表（可选，用于动态配置管理）
CREATE TABLE IF NOT EXISTS api_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE COMMENT 'API名称',
    description VARCHAR(200) COMMENT 'API描述',
    url VARCHAR(500) NOT NULL COMMENT 'API地址',
    method VARCHAR(10) DEFAULT 'GET' COMMENT 'HTTP方法',
    headers JSON COMMENT '请求头',
    params JSON COMMENT '请求参数',
    table_name VARCHAR(50) NOT NULL COMMENT '对应表名',
    field_mapping JSON COMMENT '字段映射',
    field_types JSON COMMENT '字段类型',
    sync_interval INT DEFAULT 3600 COMMENT '同步间隔(秒)',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_enabled (enabled),
    INDEX idx_table_name (table_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API配置表';

-- 插入学生基本数据API配置示例
INSERT INTO api_config (
    name, description, url, method, headers, table_name, 
    field_mapping, field_types, sync_interval, enabled
) VALUES (
    'fdm_gxxs_xsjbsj',
    '学生基本数据子类表同步',
    'http://***********:33024/1033769420954/api/v1/xs/fdm/fdm_gxxs_xsjbsj',
    'GET',
    JSON_OBJECT(
        'X-H3C-ID', '1033769420954',
        'X-H3C-APPKEY', 'bt2qifdv',
        'Content-Type', 'application/json'
    ),
    'fdm_gxxs_xsjbsj',
    JSON_OBJECT(
        'xh', 'student_number',
        'xm', 'student_name',
        'ywxm', 'english_name',
        'xmpy', 'name_pinyin',
        'yxdm', 'college_code',
        'yxmc', 'college_name',
        'zydm', 'major_code',
        'zymc', 'major_name',
        'bjdm', 'class_code',
        'bjmc', 'class_name',
        'xsdqztm', 'student_status_code',
        'xsdqzt', 'student_status',
        'dzxx', 'email',
        'xjh', 'student_id_number'
    ),
    JSON_OBJECT(
        'student_number', 'VARCHAR(20) PRIMARY KEY',
        'student_name', 'VARCHAR(50) NOT NULL',
        'english_name', 'VARCHAR(100)',
        'name_pinyin', 'VARCHAR(100)',
        'college_code', 'VARCHAR(20)',
        'college_name', 'VARCHAR(100)',
        'major_code', 'VARCHAR(20)',
        'major_name', 'VARCHAR(100)',
        'class_code', 'VARCHAR(20)',
        'class_name', 'VARCHAR(100)',
        'student_status_code', 'VARCHAR(10)',
        'student_status', 'VARCHAR(20)',
        'email', 'VARCHAR(100)',
        'student_id_number', 'VARCHAR(30)'
    ),
    3600,
    TRUE
) ON DUPLICATE KEY UPDATE
    description = VALUES(description),
    url = VALUES(url),
    headers = VALUES(headers),
    field_mapping = VALUES(field_mapping),
    field_types = VALUES(field_types),
    updated_at = CURRENT_TIMESTAMP;

-- 创建用户和授权（可选）
-- CREATE USER IF NOT EXISTS 'h3c_sync'@'localhost' IDENTIFIED BY 'your_secure_password';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON h3c_data_sync.* TO 'h3c_sync'@'localhost';
-- FLUSH PRIVILEGES;

-- 显示创建的表
SHOW TABLES;

-- 显示学生基本数据表结构
DESCRIBE fdm_gxxs_xsjbsj;
