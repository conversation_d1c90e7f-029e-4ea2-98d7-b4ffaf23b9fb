-- H3C数据同步数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS h3c_data_sync 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE h3c_data_sync;

-- 创建学生基本数据表
CREATE TABLE IF NOT EXISTS fdm_gxxs_xsjbsj (
    student_number VARCHAR(20) PRIMARY KEY COMMENT '学号',
    student_name VARCHAR(50) NOT NULL COMMENT '姓名',
    english_name VARCHAR(100) COMMENT '英文姓名',
    name_pinyin VARCHAR(100) COMMENT '姓名拼音',
    college_code VARCHAR(20) COMMENT '学院代码',
    college_name VARCHAR(100) COMMENT '学院名称',
    major_code VARCHAR(20) COMMENT '专业代码',
    major_name VARCHAR(100) COMMENT '专业名称',
    class_code VARCHAR(20) COMMENT '班级代码',
    class_name VARCHAR(100) COMMENT '班级名称',
    student_status_code VARCHAR(10) COMMENT '学生状态码',
    student_status VARCHAR(20) COMMENT '学生状态',
    email VARCHAR(100) COMMENT '电子邮箱',
    student_id_number VARCHAR(30) COMMENT '学籍号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_college_code (college_code),
    INDEX idx_major_code (major_code),
    INDEX idx_class_code (class_code),
    INDEX idx_student_status (student_status_code),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生基本数据子类表';

-- 创建院系所单位基本信息表
CREATE TABLE IF NOT EXISTS fdm_xx_dw (
    department_code VARCHAR(20) PRIMARY KEY COMMENT '单位号',
    department_name VARCHAR(200) NOT NULL COMMENT '单位名称',
    department_english_name VARCHAR(200) COMMENT '单位英文名称',
    department_short_name VARCHAR(100) COMMENT '单位简称',
    parent_department_code VARCHAR(20) COMMENT '隶属单位号',
    parent_department_name VARCHAR(200) COMMENT '隶属单位名称',
    department_head_code VARCHAR(20) COMMENT '单位负责人号',
    department_head_name VARCHAR(100) COMMENT '单位负责人名称',
    effective_date DATE COMMENT '生效日期',
    department_validity_flag VARCHAR(10) COMMENT '单位有效标识',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    INDEX idx_parent_department (parent_department_code),
    INDEX idx_department_head (department_head_code),
    INDEX idx_department_name (department_name),
    INDEX idx_validity_flag (department_validity_flag),
    INDEX idx_created_at (created_at),

    -- 外键约束（自引用）
    FOREIGN KEY (parent_department_code) REFERENCES fdm_xx_dw(department_code) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='院系所单位基本信息表';

-- 创建课程数据表
CREATE TABLE IF NOT EXISTS course_schedule (
    schedule_id VARCHAR(50) PRIMARY KEY COMMENT '排课ID',
    semester_id VARCHAR(20) COMMENT '学年学期ID',
    semester VARCHAR(20) COMMENT '学年学期',

    -- 开课信息
    course_location_code VARCHAR(10) COMMENT '开课地点代码',
    course_location_type VARCHAR(20) COMMENT '开课地点类别',
    time_flag VARCHAR(20) COMMENT '时间标志',
    course_weeks VARCHAR(100) COMMENT '开课周次',
    course_weeks_detail VARCHAR(200) COMMENT '开课周次明细',

    -- 课程时间信息
    course_time_code VARCHAR(20) COMMENT '课程时间编号',
    course_time VARCHAR(20) COMMENT '课程时间',
    course_time_detail VARCHAR(100) COMMENT '课程时间明细',
    start_time TIME COMMENT '开始时间',
    end_time TIME COMMENT '结束时间',
    weekday VARCHAR(5) COMMENT '星期',

    -- 排课信息
    schedule_category VARCHAR(50) COMMENT '排课类别',
    substitute_flag VARCHAR(10) COMMENT '代课标志',
    substitute_ip VARCHAR(50) COMMENT '代课IP',
    substitute_teacher_id VARCHAR(50) COMMENT '代课教师ID',

    -- 教师信息
    teacher_id VARCHAR(50) COMMENT '教师ID',
    old_teacher_id VARCHAR(50) COMMENT '原教师ID',
    is_record_deleted VARCHAR(5) COMMENT '记录是否删除',

    -- 课表基础信息
    course_basic_id VARCHAR(50) COMMENT '课表基础描述ID',

    -- 操作信息
    operator VARCHAR(50) COMMENT '操作人',
    operation_time DATETIME COMMENT '操作时间',
    create_time DATETIME COMMENT '创建时间',
    update_time DATETIME COMMENT '更新时间',

    -- 排课班级
    schedule_class VARCHAR(100) COMMENT '排课班级',

    -- 录播相关
    recording_reason VARCHAR(100) COMMENT '录播原因',
    link_name VARCHAR(100) COMMENT '链接名称',
    qr_code_name VARCHAR(100) COMMENT '二维码名称',
    qr_code_address VARCHAR(200) COMMENT '二维码地址',
    link_type VARCHAR(20) COMMENT '链接类型',

    -- 实验室相关
    is_special_lab VARCHAR(5) COMMENT '是否特殊实验室',
    lab_reason_id VARCHAR(50) COMMENT '实验室原因ID',

    -- 上课时间
    actual_start_time TIME COMMENT '实际开始时间',
    actual_end_time TIME COMMENT '实际结束时间',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',

    -- 索引
    INDEX idx_semester (semester_id),
    INDEX idx_teacher (teacher_id),
    INDEX idx_course_basic (course_basic_id),
    INDEX idx_weekday_time (weekday, start_time),
    INDEX idx_course_weeks (course_weeks),
    INDEX idx_create_time (create_time),
    INDEX idx_schedule_category (schedule_category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='课程数据表';

-- 创建数据同步日志表
CREATE TABLE IF NOT EXISTS sync_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    api_name VARCHAR(50) NOT NULL COMMENT 'API名称',
    table_name VARCHAR(50) NOT NULL COMMENT '表名',
    sync_mode VARCHAR(20) NOT NULL COMMENT '同步模式',
    records_count INT DEFAULT 0 COMMENT '同步记录数',
    status VARCHAR(20) NOT NULL COMMENT '同步状态: success, failed',
    error_message TEXT COMMENT '错误信息',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    duration_ms INT DEFAULT 0 COMMENT '耗时(毫秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_api_name (api_name),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据同步日志表';

-- 创建API配置表（可选，用于动态配置管理）
CREATE TABLE IF NOT EXISTS api_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE COMMENT 'API名称',
    description VARCHAR(200) COMMENT 'API描述',
    url VARCHAR(500) NOT NULL COMMENT 'API地址',
    method VARCHAR(10) DEFAULT 'GET' COMMENT 'HTTP方法',
    headers JSON COMMENT '请求头',
    params JSON COMMENT '请求参数',
    table_name VARCHAR(50) NOT NULL COMMENT '对应表名',
    field_mapping JSON COMMENT '字段映射',
    field_types JSON COMMENT '字段类型',
    sync_interval INT DEFAULT 3600 COMMENT '同步间隔(秒)',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_enabled (enabled),
    INDEX idx_table_name (table_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API配置表';

-- 插入学生基本数据API配置示例
INSERT INTO api_config (
    name, description, url, method, headers, table_name, 
    field_mapping, field_types, sync_interval, enabled
) VALUES (
    'fdm_gxxs_xsjbsj',
    '学生基本数据子类表同步',
    'http://***********:33024/1033769420954/api/v1/xs/fdm/fdm_gxxs_xsjbsj',
    'GET',
    JSON_OBJECT(
        'X-H3C-ID', '1033769420954',
        'X-H3C-APPKEY', 'bt2qifdv',
        'Content-Type', 'application/json'
    ),
    'fdm_gxxs_xsjbsj',
    JSON_OBJECT(
        'xh', 'student_number',
        'xm', 'student_name',
        'ywxm', 'english_name',
        'xmpy', 'name_pinyin',
        'yxdm', 'college_code',
        'yxmc', 'college_name',
        'zydm', 'major_code',
        'zymc', 'major_name',
        'bjdm', 'class_code',
        'bjmc', 'class_name',
        'xsdqztm', 'student_status_code',
        'xsdqzt', 'student_status',
        'dzxx', 'email',
        'xjh', 'student_id_number'
    ),
    JSON_OBJECT(
        'student_number', 'VARCHAR(20) PRIMARY KEY',
        'student_name', 'VARCHAR(50) NOT NULL',
        'english_name', 'VARCHAR(100)',
        'name_pinyin', 'VARCHAR(100)',
        'college_code', 'VARCHAR(20)',
        'college_name', 'VARCHAR(100)',
        'major_code', 'VARCHAR(20)',
        'major_name', 'VARCHAR(100)',
        'class_code', 'VARCHAR(20)',
        'class_name', 'VARCHAR(100)',
        'student_status_code', 'VARCHAR(10)',
        'student_status', 'VARCHAR(20)',
        'email', 'VARCHAR(100)',
        'student_id_number', 'VARCHAR(30)'
    ),
    3600,
    TRUE
) ON DUPLICATE KEY UPDATE
    description = VALUES(description),
    url = VALUES(url),
    headers = VALUES(headers),
    field_mapping = VALUES(field_mapping),
    field_types = VALUES(field_types),
    updated_at = CURRENT_TIMESTAMP;

-- 插入院系所单位基本信息API配置
INSERT INTO api_config (
    name, description, url, method, headers, table_name,
    field_mapping, field_types, sync_interval, enabled
) VALUES (
    'fdm_xx_dw',
    '院系所单位基本信息同步',
    'http://***********:33024/1033769420954/data_center/xx/v1/fdm_xx_dw',
    'GET',
    JSON_OBJECT(
        'X-H3C-ID', '1033769420954',
        'X-H3C-APPKEY', 'bt2qifdv',
        'Content-Type', 'application/json'
    ),
    'fdm_xx_dw',
    JSON_OBJECT(
        'dwh', 'department_code',
        'dwmc', 'department_name',
        'dwywmc', 'department_english_name',
        'dwjc', 'department_short_name',
        'lsdwh', 'parent_department_code',
        'lsdwh_mc', 'parent_department_name',
        'dwfzrh', 'department_head_code',
        'dwfzrh_mc', 'department_head_name',
        'sxrq', 'effective_date',
        'dwyxbs', 'department_validity_flag'
    ),
    JSON_OBJECT(
        'department_code', 'VARCHAR(20) PRIMARY KEY',
        'department_name', 'VARCHAR(200) NOT NULL',
        'department_english_name', 'VARCHAR(200)',
        'department_short_name', 'VARCHAR(100)',
        'parent_department_code', 'VARCHAR(20)',
        'parent_department_name', 'VARCHAR(200)',
        'department_head_code', 'VARCHAR(20)',
        'department_head_name', 'VARCHAR(100)',
        'effective_date', 'DATE',
        'department_validity_flag', 'VARCHAR(10)'
    ),
    7200,
    TRUE
) ON DUPLICATE KEY UPDATE
    description = VALUES(description),
    url = VALUES(url),
    headers = VALUES(headers),
    field_mapping = VALUES(field_mapping),
    field_types = VALUES(field_types),
    sync_interval = VALUES(sync_interval),
    updated_at = CURRENT_TIMESTAMP;

-- 插入课程数据API配置
INSERT INTO api_config (
    name, description, url, method, headers, table_name,
    field_mapping, field_types, sync_interval, enabled
) VALUES (
    'course_schedule',
    '课程数据表同步',
    'http://***********:33024/api/v1/fdm/gxjx/fdm_gxjx_kbsj',
    'GET',
    JSON_OBJECT(
        'X-H3C-ID', '1033769420954',
        'X-H3C-APPKEY', 'bt2qifdv',
        'Content-Type', 'application/json'
    ),
    'course_schedule',
    JSON_OBJECT(
        'apid', 'schedule_id',
        'xnxqid', 'semester_id',
        'xnxq', 'semester',
        'kkd', 'course_location_code',
        'kkdlb', 'course_location_type',
        'sjbz', 'time_flag',
        'kkzc', 'course_weeks',
        'kkzcmx', 'course_weeks_detail',
        'kcsjbh', 'course_time_code',
        'kcsj', 'course_time',
        'kcsjmx', 'course_time_detail',
        'kssj', 'start_time',
        'jssj', 'end_time',
        'xq', 'weekday',
        'pklb', 'schedule_category',
        'dkbz', 'substitute_flag',
        'dkip', 'substitute_ip',
        'dkrjsid', 'substitute_teacher_id',
        'jsid', 'teacher_id',
        'oldjsid', 'old_teacher_id',
        'jlsfsc', 'is_record_deleted',
        'kbjcmsid', 'course_basic_id',
        'czr', 'operator',
        'czsj', 'operation_time',
        'cjsj', 'create_time',
        'gxsj', 'update_time',
        'pkbj', 'schedule_class',
        'lpyy', 'recording_reason',
        'ljmc', 'link_name',
        'ewmmc', 'qr_code_name',
        'ewmdz', 'qr_code_address',
        'ljlx', 'link_type',
        'sftssy', 'is_special_lab',
        'syyyid', 'lab_reason_id',
        'skkssj', 'actual_start_time',
        'skjssj', 'actual_end_time'
    ),
    JSON_OBJECT(
        'schedule_id', 'VARCHAR(50) PRIMARY KEY',
        'semester_id', 'VARCHAR(20)',
        'semester', 'VARCHAR(20)',
        'course_location_code', 'VARCHAR(10)',
        'course_location_type', 'VARCHAR(20)',
        'time_flag', 'VARCHAR(20)',
        'course_weeks', 'VARCHAR(100)',
        'course_weeks_detail', 'VARCHAR(200)',
        'course_time_code', 'VARCHAR(20)',
        'course_time', 'VARCHAR(20)',
        'course_time_detail', 'VARCHAR(100)',
        'start_time', 'TIME',
        'end_time', 'TIME',
        'weekday', 'VARCHAR(5)',
        'schedule_category', 'VARCHAR(50)',
        'substitute_flag', 'VARCHAR(10)',
        'substitute_ip', 'VARCHAR(50)',
        'substitute_teacher_id', 'VARCHAR(50)',
        'teacher_id', 'VARCHAR(50)',
        'old_teacher_id', 'VARCHAR(50)',
        'is_record_deleted', 'VARCHAR(5)',
        'course_basic_id', 'VARCHAR(50)',
        'operator', 'VARCHAR(50)',
        'operation_time', 'DATETIME',
        'create_time', 'DATETIME',
        'update_time', 'DATETIME',
        'schedule_class', 'VARCHAR(100)',
        'recording_reason', 'VARCHAR(100)',
        'link_name', 'VARCHAR(100)',
        'qr_code_name', 'VARCHAR(100)',
        'qr_code_address', 'VARCHAR(200)',
        'link_type', 'VARCHAR(20)',
        'is_special_lab', 'VARCHAR(5)',
        'lab_reason_id', 'VARCHAR(50)',
        'actual_start_time', 'TIME',
        'actual_end_time', 'TIME'
    ),
    1800,
    TRUE
) ON DUPLICATE KEY UPDATE
    description = VALUES(description),
    url = VALUES(url),
    headers = VALUES(headers),
    field_mapping = VALUES(field_mapping),
    field_types = VALUES(field_types),
    sync_interval = VALUES(sync_interval),
    updated_at = CURRENT_TIMESTAMP;

-- 创建用户和授权（可选）
-- CREATE USER IF NOT EXISTS 'h3c_sync'@'localhost' IDENTIFIED BY 'your_secure_password';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON h3c_data_sync.* TO 'h3c_sync'@'localhost';
-- FLUSH PRIVILEGES;

-- 显示创建的表
SHOW TABLES;

-- 显示学生基本数据表结构
DESCRIBE fdm_gxxs_xsjbsj;
