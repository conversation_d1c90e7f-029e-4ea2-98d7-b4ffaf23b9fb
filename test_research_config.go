package main

import (
	"encoding/json"
	"fmt"
	"log"

	"api2mysql/config"
)

func main() {
	fmt.Println("测试科研项目表配置...")

	// 加载配置文件
	cfg, err := config.LoadConfig("config-production.yaml")
	if err != nil {
		log.Fatalf("加载配置文件失败: %v", err)
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		log.Fatalf("配置验证失败: %v", err)
	}

	// 查找科研项目API配置
	var researchAPI *config.APIConfig
	for _, api := range cfg.APIs {
		if api.Name == "research_project" {
			researchAPI = &api
			break
		}
	}

	if researchAPI == nil {
		log.Fatalf("未找到 research_project API配置")
	}

	fmt.Printf("✅ 找到科研项目API配置: %s\n", researchAPI.Description)
	fmt.Printf("📊 配置统计:\n")
	fmt.Printf("  - API地址: %s\n", researchAPI.URL)
	fmt.Printf("  - 字段映射数量: %d\n", len(researchAPI.FieldMapping))
	fmt.Printf("  - 字段类型数量: %d\n", len(researchAPI.FieldTypes))
	fmt.Printf("  - 字段注释数量: %d\n", len(researchAPI.FieldComments))
	fmt.Printf("  - 分页参数: pageSize=%s, pageNum=%s\n", 
		researchAPI.Params["pageSize"], researchAPI.Params["pageNum"])

	// 验证字段映射和字段类型的一致性
	fmt.Println("\n🔍 验证字段映射和类型定义的一致性...")
	missingTypes := []string{}
	missingComments := []string{}

	for apiField, dbField := range researchAPI.FieldMapping {
		// 检查字段类型是否存在
		if _, exists := researchAPI.FieldTypes[dbField]; !exists {
			missingTypes = append(missingTypes, fmt.Sprintf("%s -> %s", apiField, dbField))
		}
		
		// 检查字段注释是否存在
		if _, exists := researchAPI.FieldComments[dbField]; !exists {
			missingComments = append(missingComments, fmt.Sprintf("%s -> %s", apiField, dbField))
		}
	}

	if len(missingTypes) > 0 {
		fmt.Printf("❌ 缺少字段类型定义的字段:\n")
		for _, field := range missingTypes {
			fmt.Printf("  - %s\n", field)
		}
	} else {
		fmt.Printf("✅ 所有映射字段都有对应的类型定义\n")
	}

	if len(missingComments) > 0 {
		fmt.Printf("⚠️  缺少字段注释的字段:\n")
		for _, field := range missingComments {
			fmt.Printf("  - %s\n", field)
		}
	} else {
		fmt.Printf("✅ 所有映射字段都有对应的注释\n")
	}

	// 模拟JSON数据测试
	fmt.Println("\n🧪 测试JSON数据解析...")
	testJSON := `{
		"id": "FE6F299D238B4EFAB7331F324F0B5711",
		"xh": "00001549",
		"xm": "刘娟娟",
		"kcbid": "010827",
		"kcbh": "j1000169",
		"kcmc": "计算机基础",
		"xnxqid": "2000-2001-1",
		"xqmc": "2000-2001-1",
		"ksxzm": "0",
		"ksxzmc": "正常考试",
		"kkbmbh": "01",
		"kkbm": "航空制造工程学院",
		"khfsm": "1",
		"khfsmc": "补考一",
		"zcj": "91.0",
		"zxs": "112.0",
		"xf": "3.0",
		"cjfs": "D",
		"cjfsmc": "分数方式",
		"kclbm": "1",
		"kclbmc": "必修",
		"kcxzm": "12",
		"kcxzmc": "其他",
		"jsxm": "",
		"cjbs": "",
		"cjbsmz": "",
		"sffxkc": "否",
		"sfsxwkc": "否",
		"cjr": "qianyi",
		"cjsj": "2023-11-28 11:31:03",
		"xgr": "",
		"xgsj": "",
		"jlsfsc": "否",
		"bz": "",
		"yxdm": "300100",
		"yxmc": "航空制造工程学院",
		"zybh": "0104",
		"zymc": "数控技术（五年制）",
		"bjbh": "000015",
		"bjmc": "00数控五一班"
	}`

	var jsonData map[string]interface{}
	if err := json.Unmarshal([]byte(testJSON), &jsonData); err != nil {
		log.Printf("JSON解析失败: %v", err)
		return
	}

	// 模拟数据转换
	transformedData := make(map[string]interface{})
	mappedFields := 0
	
	for apiField, dbField := range researchAPI.FieldMapping {
		if value, exists := jsonData[apiField]; exists {
			transformedData[dbField] = value
			mappedFields++
		}
	}

	fmt.Printf("✅ 成功映射 %d 个字段\n", mappedFields)
	fmt.Printf("📋 转换后的数据示例:\n")
	
	// 显示部分转换结果
	sampleFields := []string{
		"record_id", "student_number", "student_name", "course_name", 
		"semester_name", "teaching_dept_name", "total_score", "credit_points",
		"course_category_name", "score_record_time", "college_name", "major_name", "class_name"
	}
	
	for _, field := range sampleFields {
		if value, exists := transformedData[field]; exists {
			comment := researchAPI.FieldComments[field]
			fmt.Printf("  %-25s: %v (%s)\n", field, value, comment)
		}
	}

	// 显示字段分类统计
	fmt.Println("\n📋 字段分类统计:")
	categories := map[string][]string{
		"基本信息": {"record_id", "student_number", "student_name"},
		"课程信息": {"course_id", "course_code", "course_name", "semester_id", "semester_name"},
		"成绩信息": {"total_score", "total_hours", "credit_points", "score_method", "score_method_name"},
		"学院专业": {"college_code", "college_name", "major_code", "major_name", "class_code", "class_name"},
		"操作信息": {"score_recorder", "score_record_time", "modifier", "modify_time", "is_record_deleted"},
	}

	for category, fields := range categories {
		fmt.Printf("\n  %s:\n", category)
		count := 0
		for _, field := range fields {
			if _, exists := transformedData[field]; exists {
				count++
			}
		}
		fmt.Printf("    已映射字段: %d/%d\n", count, len(fields))
	}

	// 分析成绩数据
	fmt.Println("\n📊 成绩数据分析:")
	if score, ok := transformedData["total_score"].(string); ok {
		fmt.Printf("  总成绩: %s\n", score)
	}
	if hours, ok := transformedData["total_hours"].(string); ok {
		fmt.Printf("  总学时: %s\n", hours)
	}
	if credit, ok := transformedData["credit_points"].(string); ok {
		fmt.Printf("  学分: %s\n", credit)
	}
	if category, ok := transformedData["course_category_name"].(string); ok {
		fmt.Printf("  课程类别: %s\n", category)
	}

	fmt.Println("\n🎉 科研项目配置验证完成！")
	fmt.Println("\n📝 下一步:")
	fmt.Println("1. 编译程序: go build -o api2mysql")
	fmt.Println("2. 测试同步: ./api2mysql -config config-production.yaml -sync-once research_project")
	fmt.Println("3. 检查数据库表结构和数据")
	fmt.Println("4. 验证分页功能是否正常工作")
}
