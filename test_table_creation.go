package main

import (
	"fmt"
	"log"

	"github.com/sirupsen/logrus"

	"api2mysql/config"
	"api2mysql/database"
)

func main() {
	// 创建日志器
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// 测试配置
	dbConfig := &config.DatabaseConfig{
		Host:            "localhost",
		Port:            3306,
		Username:        "root",
		Password:        "password", // 请根据实际情况修改
		Database:        "test_db",
		Charset:         "utf8mb4",
		MaxOpenConns:    10,
		MaxIdleConns:    5,
		ConnMaxLifetime: 300,
	}

	fmt.Println("测试表创建逻辑...")
	fmt.Printf("数据库配置: %s@%s:%d/%s\n", dbConfig.Username, dbConfig.Host, dbConfig.Port, dbConfig.Database)

	// 创建数据库管理器
	dbManager, err := database.NewMySQLManager(dbConfig, logger)
	if err != nil {
		log.Fatalf("创建数据库管理器失败: %v", err)
	}
	defer dbManager.Close()

	// 测试表配置
	tableName := "test_table_with_comments"
	fieldTypes := map[string]string{
		"id":          "VARCHAR(20) PRIMARY KEY",
		"name":        "VARCHAR(100) NOT NULL",
		"description": "TEXT",
		"amount":      "DECIMAL(10,2)",
		"is_active":   "BOOLEAN DEFAULT TRUE",
		"created_at":  "TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
		"updated_at":  "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP",
	}

	fieldComments := map[string]string{
		"id":          "主键ID",
		"name":        "名称",
		"description": "描述信息",
		"amount":      "金额",
		"is_active":   "是否激活",
		"created_at":  "创建时间",
		"updated_at":  "更新时间",
	}

	fmt.Printf("\n创建测试表: %s\n", tableName)

	// 测试创建表
	err = dbManager.CreateTableIfNotExists(tableName, fieldTypes, fieldComments)
	if err != nil {
		log.Fatalf("创建表失败: %v", err)
	}

	fmt.Printf("✅ 表 %s 创建成功\n", tableName)

	// 再次调用，测试"如果不存在"的逻辑
	fmt.Printf("\n再次调用创建表方法（测试IF NOT EXISTS逻辑）...\n")
	err = dbManager.CreateTableIfNotExists(tableName, fieldTypes, fieldComments)
	if err != nil {
		log.Fatalf("第二次创建表失败: %v", err)
	}

	fmt.Printf("✅ 表 %s 已存在，跳过创建\n", tableName)

	fmt.Println("\n🎉 表创建逻辑测试完成！")
	fmt.Println("\n📝 请检查数据库中的表结构，确认字段注释是否正确添加")
	fmt.Printf("SQL查询: SHOW CREATE TABLE %s;\n", tableName)
	fmt.Printf("或者: DESCRIBE %s;\n", tableName)
}
