package main

import (
	"encoding/json"
	"fmt"
	"log"

	"api2mysql/config"
)

func main() {
	fmt.Println("测试教师基本信息配置...")

	// 加载配置文件
	cfg, err := config.LoadConfig("config-production.yaml")
	if err != nil {
		log.Fatalf("加载配置文件失败: %v", err)
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		log.Fatalf("配置验证失败: %v", err)
	}

	// 查找教师基本信息API配置
	var teacherAPI *config.APIConfig
	for _, api := range cfg.APIs {
		if api.Name == "teacher_basic" {
			teacherAPI = &api
			break
		}
	}

	if teacherAPI == nil {
		log.Fatalf("未找到 teacher_basic API配置")
	}

	fmt.Printf("✅ 找到教师基本信息API配置: %s\n", teacherAPI.Description)
	fmt.Printf("📊 配置统计:\n")
	fmt.Printf("  - 字段映射数量: %d\n", len(teacherAPI.FieldMapping))
	fmt.Printf("  - 字段类型数量: %d\n", len(teacherAPI.FieldTypes))
	fmt.Printf("  - 字段注释数量: %d\n", len(teacherAPI.FieldComments))

	// 验证字段映射和字段类型的一致性
	fmt.Println("\n🔍 验证字段映射和类型定义的一致性...")
	missingTypes := []string{}
	missingComments := []string{}

	for apiField, dbField := range teacherAPI.FieldMapping {
		// 检查字段类型是否存在
		if _, exists := teacherAPI.FieldTypes[dbField]; !exists {
			missingTypes = append(missingTypes, fmt.Sprintf("%s -> %s", apiField, dbField))
		}
		
		// 检查字段注释是否存在
		if _, exists := teacherAPI.FieldComments[dbField]; !exists {
			missingComments = append(missingComments, fmt.Sprintf("%s -> %s", apiField, dbField))
		}
	}

	if len(missingTypes) > 0 {
		fmt.Printf("❌ 缺少字段类型定义的字段:\n")
		for _, field := range missingTypes {
			fmt.Printf("  - %s\n", field)
		}
	} else {
		fmt.Printf("✅ 所有映射字段都有对应的类型定义\n")
	}

	if len(missingComments) > 0 {
		fmt.Printf("⚠️  缺少字段注释的字段:\n")
		for _, field := range missingComments {
			fmt.Printf("  - %s\n", field)
		}
	} else {
		fmt.Printf("✅ 所有映射字段都有对应的注释\n")
	}

	// 模拟JSON数据测试
	fmt.Println("\n🧪 测试JSON数据解析...")
	testJSON := `{
		"gh": "19853810002",
		"xm": "魏书江",
		"gjm": "156",
		"gj": "中国",
		"mzm": "01",
		"mz": "汉族",
		"zjhm": "61011419690721001X",
		"xbm": "1",
		"xb": "男性",
		"csrq": "1969-07-21",
		"prsj": "1985-07-01",
		"rzrq": "1985-07-01",
		"dwbm": "203800",
		"dwmc": "国有资产管理处",
		"gwlb": "干事",
		"sfzb": "1",
		"zc": "高级工程师",
		"byyx": "空工大",
		"sxzy": "计算机科学与技术",
		"jzgdqztm": "01",
		"jzgdqztmc": "在职",
		"jzgdqzt": "在职",
		"gjmc": "中国",
		"mzmc": "汉族",
		"xbmc": "男性"
	}`

	var jsonData map[string]interface{}
	if err := json.Unmarshal([]byte(testJSON), &jsonData); err != nil {
		log.Printf("JSON解析失败: %v", err)
		return
	}

	// 模拟数据转换
	transformedData := make(map[string]interface{})
	mappedFields := 0
	
	for apiField, dbField := range teacherAPI.FieldMapping {
		if value, exists := jsonData[apiField]; exists {
			transformedData[dbField] = value
			mappedFields++
		}
	}

	fmt.Printf("✅ 成功映射 %d 个字段\n", mappedFields)
	fmt.Printf("📋 转换后的数据示例:\n")
	
	// 显示部分转换结果
	sampleFields := []string{
		"teacher_number", "teacher_name", "gender", "birth_date", 
		"country", "ethnicity", "department_name", "professional_title",
		"graduate_school", "major", "current_status",
	}
	
	for _, field := range sampleFields {
		if value, exists := transformedData[field]; exists {
			comment := teacherAPI.FieldComments[field]
			fmt.Printf("  %-25s: %v (%s)\n", field, value, comment)
		}
	}

	// 显示字段分类统计
	fmt.Println("\n📋 字段分类统计:")
	categories := map[string][]string{
		"基本信息": {"teacher_number", "teacher_name", "id_number", "gender", "birth_date"},
		"国籍民族": {"country", "ethnicity", "origin"},
		"工作信息": {"department_name", "position_category", "professional_title", "entry_date"},
		"学历信息": {"highest_education", "highest_degree", "graduate_school", "major"},
		"状态信息": {"current_status", "staff_category", "is_on_duty"},
	}

	for category, fields := range categories {
		fmt.Printf("\n  %s:\n", category)
		count := 0
		for _, field := range fields {
			if _, exists := transformedData[field]; exists {
				count++
			}
		}
		fmt.Printf("    已映射字段: %d/%d\n", count, len(fields))
	}

	fmt.Println("\n🎉 教师配置验证完成！")
	fmt.Println("\n📝 下一步:")
	fmt.Println("1. 编译程序: go build -o api2mysql")
	fmt.Println("2. 测试同步: ./api2mysql -config config-production.yaml -sync-once teacher_basic")
	fmt.Println("3. 检查数据库表结构和数据")
}
