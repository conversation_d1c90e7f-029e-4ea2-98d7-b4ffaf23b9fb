version: '3.8'

services:
  api2mysql:
    build: .
    container_name: api2mysql-service
    restart: unless-stopped
    
    # 挂载外部配置文件和日志目录
    volumes:
      # 挂载配置文件目录（将本地配置文件放在 ./config 目录下）
      - ./config:/app/config:ro
      # 挂载日志目录（日志将保存到本地 ./logs 目录）
      - ./logs:/app/logs
    
    # 环境变量配置
    environment:
      - LOG_LEVEL=info
      # 可以通过环境变量覆盖其他配置
      # - DB_HOST=your_db_host
      # - DB_PORT=3306
      # - DB_USERNAME=your_username
      # - DB_PASSWORD=your_password
    
    # 网络配置（如果需要）
    # networks:
    #   - api2mysql-network
    
    # 依赖服务（如果有数据库服务）
    # depends_on:
    #   - mysql

  # 可选：MySQL 数据库服务
  # mysql:
  #   image: mysql:8.0
  #   container_name: api2mysql-mysql
  #   restart: unless-stopped
  #   environment:
  #     MYSQL_ROOT_PASSWORD: your_root_password
  #     MYSQL_DATABASE: api2mysql
  #     MYSQL_USER: api2mysql_user
  #     MYSQL_PASSWORD: your_password
  #   volumes:
  #     - mysql_data:/var/lib/mysql
  #     - ./init_database.sql:/docker-entrypoint-initdb.d/init_database.sql:ro
  #   ports:
  #     - "3306:3306"
  #   networks:
  #     - api2mysql-network

# 网络配置
# networks:
#   api2mysql-network:
#     driver: bridge

# 数据卷配置
# volumes:
#   mysql_data:
