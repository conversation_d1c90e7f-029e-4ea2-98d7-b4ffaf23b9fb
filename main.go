package main

import (
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/sirupsen/logrus"

	"api2mysql/config"
	"api2mysql/sync"
)

var (
	configPath = flag.String("config", "config.yaml", "配置文件路径")
	logLevel   = flag.String("log-level", "", "日志级别 (debug, info, warn, error)")
	syncOnce   = flag.String("sync-once", "", "执行一次性同步指定的API")
	showStatus = flag.Bool("status", false, "显示同步状态")
	version    = flag.Bool("version", false, "显示版本信息")
)

const (
	AppName    = "api2mysql"
	AppVersion = "1.0.0"
)

func main() {
	flag.Parse()

	// 显示版本信息
	if *version {
		fmt.Printf("%s version %s\n", AppName, AppVersion)
		return
	}

	// 加载配置
	cfg, err := config.LoadConfig(*configPath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "加载配置文件失败: %v\n", err)
		os.Exit(1)
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		fmt.Fprintf(os.Stderr, "配置验证失败: %v\n", err)
		os.Exit(1)
	}

	// 设置日志
	logger := setupLogger(cfg)
	logger.Infof("启动 %s v%s", AppName, AppVersion)
	logger.Infof("使用配置文件: %s", *configPath)

	// 处理不同的运行模式
	if *showStatus {
		// 显示状态（不需要数据库连接）
		var enabledAPIs []string
		for _, apiConfig := range cfg.APIs {
			if apiConfig.Enabled {
				enabledAPIs = append(enabledAPIs, apiConfig.Name)
			}
		}

		fmt.Printf("同步状态:\n")
		fmt.Printf("  启用的API: %v\n", enabledAPIs)
		fmt.Printf("  同步模式: %s\n", cfg.Global.SyncMode)
		fmt.Printf("  重试次数: %d\n", cfg.Global.RetryCount)
		fmt.Printf("  请求超时: %d秒\n", cfg.Global.RequestTimeout)
		fmt.Printf("  数据库: %s@%s:%d/%s\n", cfg.Database.Username, cfg.Database.Host, cfg.Database.Port, cfg.Database.Database)
		return
	}

	// 创建同步器
	syncer, err := sync.NewSyncer(cfg, logger)
	if err != nil {
		logger.Fatalf("创建同步器失败: %v", err)
	}

	if *syncOnce != "" {
		// 执行一次性同步
		logger.Infof("执行一次性同步: %s", *syncOnce)
		err := syncer.SyncOnce(*syncOnce)
		if err != nil {
			logger.Errorf("一次性同步失败: %v", err)
			os.Exit(1)
		}
		logger.Infof("一次性同步完成: %s", *syncOnce)
		return
	}

	// 启动持续同步服务
	err = syncer.Start()
	if err != nil {
		logger.Fatalf("启动同步服务失败: %v", err)
	}

	// 等待中断信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	logger.Info("服务已启动，按 Ctrl+C 停止")
	<-sigChan

	logger.Info("收到停止信号，正在关闭服务...")
	syncer.Stop()
	logger.Info("服务已停止")
}

// setupLogger 设置日志
func setupLogger(cfg *config.Config) *logrus.Logger {
	logger := logrus.New()

	// 设置日志级别
	logLevelStr := cfg.Global.LogLevel
	if *logLevel != "" {
		logLevelStr = *logLevel
	}

	level, err := logrus.ParseLevel(logLevelStr)
	if err != nil {
		logger.Warnf("无效的日志级别 '%s'，使用默认级别 'info'", logLevelStr)
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)

	// 设置日志格式
	logger.SetFormatter(&logrus.TextFormatter{
		FullTimestamp:   true,
		TimestampFormat: "2006-01-02 15:04:05",
	})

	// 设置日志输出
	if cfg.Global.LogFile != "" {
		file, err := os.OpenFile(cfg.Global.LogFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			logger.Warnf("无法打开日志文件 '%s': %v，使用标准输出", cfg.Global.LogFile, err)
		} else {
			logger.SetOutput(file)
			logger.Infof("日志输出到文件: %s", cfg.Global.LogFile)
		}
	}

	return logger
}

// printUsage 打印使用说明
func printUsage() {
	fmt.Printf(`%s - API数据同步到MySQL工具

使用方法:
  %s [选项]

选项:
  -config string
        配置文件路径 (默认: "config.yaml")
  -log-level string
        日志级别 (debug, info, warn, error)
  -sync-once string
        执行一次性同步指定的API
  -status
        显示同步状态
  -version
        显示版本信息

示例:
  # 使用默认配置启动服务
  %s

  # 使用指定配置文件启动服务
  %s -config /path/to/config.yaml

  # 执行一次性同步
  %s -sync-once users

  # 显示同步状态
  %s -status

  # 设置日志级别
  %s -log-level debug

配置文件示例:
  参考 config.yaml 文件中的配置说明

`, AppName, AppName, AppName, AppName, AppName, AppName, AppName)
}

func init() {
	flag.Usage = printUsage
}
