# API2MySQL - API数据同步到MySQL工具

一个用Go语言开发的工具，可以从配置的API接口获取数据并自动同步到MySQL数据库。支持多个API接口配置，每个接口对应一个MySQL表，通过配置文件管理所有设置。

## 功能特性

- 🔄 **自动同步**: 支持定时从API获取数据并同步到MySQL
- 📝 **配置驱动**: 通过YAML配置文件管理所有API和数据库设置
- 🗄️ **动态建表**: 根据配置自动创建MySQL表结构
- 🔀 **字段映射**: 支持API字段到数据库字段的灵活映射
- 📊 **多种同步模式**: 支持替换、追加、更新插入三种同步模式
- 🔁 **重试机制**: 内置请求失败重试机制
- 📋 **详细日志**: 完整的日志记录和错误处理
- 🎯 **嵌套字段支持**: 支持获取API响应中的嵌套字段

## 快速开始

### 1. 安装依赖

确保你的系统已安装Go 1.17+和MySQL数据库。

```bash
# 克隆项目
git clone <repository-url>
cd api2mysql

# 安装依赖
go mod tidy
```

### 2. 配置数据库

创建MySQL数据库：

```sql
CREATE DATABASE api_sync CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. 配置文件

复制并修改配置文件：

```bash
cp config.yaml config-prod.yaml
```

编辑 `config-prod.yaml`，修改数据库连接信息：

```yaml
database:
  host: "localhost"
  port: 3306
  username: "your_username"
  password: "your_password"
  database: "api_sync"
```

### 4. 运行程序

```bash
# 编译程序
go build -o api2mysql
# 支持 RAM x86 架构的打包命令
GOARCH=amd64 go build -o api2mysql-x86
# linux 平台
GOOS=linux GOARCH=amd64 go build -o api2mysql-x86

# 使用默认配置运行
./api2mysql

# 使用指定配置文件运行
./api2mysql -config config-prod.yaml

# 执行一次性同步
./api2mysql -sync-once users

# 查看同步状态
./api2mysql -status
```

## 配置说明

### 数据库配置

```yaml
database:
  host: "localhost"          # MySQL主机地址
  port: 3306                # MySQL端口
  username: "root"          # 用户名
  password: "password"      # 密码
  database: "api_sync"      # 数据库名
  charset: "utf8mb4"        # 字符集
  max_open_conns: 10        # 最大连接数
  max_idle_conns: 5         # 最大空闲连接数
  conn_max_lifetime: 300    # 连接最大生存时间（秒）
```

### API配置

```yaml
apis:
  - name: "users"                    # API名称（唯一标识）
    description: "用户信息同步"        # 描述
    url: "https://api.example.com/users"  # API地址
    method: "GET"                    # HTTP方法
    headers:                         # 请求头
      Content-Type: "application/json"
      Authorization: "Bearer token"
    params:                          # 查询参数
      page: "1"
      limit: "100"
    table_name: "users"              # 对应的MySQL表名
    field_mapping:                   # 字段映射（API字段 -> 数据库字段）
      id: "id"
      name: "name"
      email: "email"
      address.city: "city"           # 支持嵌套字段
    field_types:                     # 数据库字段类型
      id: "INT PRIMARY KEY"
      name: "VARCHAR(100)"
      email: "VARCHAR(100)"
      city: "VARCHAR(50)"
    sync_interval: 300               # 同步间隔（秒）
    enabled: true                    # 是否启用
```

### 全局配置

```yaml
global:
  log_level: "info"              # 日志级别
  log_file: "api2mysql.log"     # 日志文件路径
  sync_mode: "replace"           # 同步模式
  retry_count: 3                 # 重试次数
  request_timeout: 30            # 请求超时时间（秒）
```

## 同步模式

- **replace**: 替换模式，每次同步前清空表，然后插入新数据
- **append**: 追加模式，直接插入新数据，不删除原有数据
- **upsert**: 更新插入模式，存在则更新，不存在则插入（需要主键）

## 字段映射

支持以下字段映射方式：

```yaml
field_mapping:
  # 简单字段映射
  id: "user_id"
  name: "user_name"
  
  # 嵌套字段映射
  address.street: "address_street"
  address.city: "address_city"
  profile.avatar.url: "avatar_url"
```

## 命令行选项

```bash
# 基本选项
-config string      配置文件路径 (默认: "config.yaml")
-log-level string   日志级别 (debug, info, warn, error)

# 运行模式
-sync-once string   执行一次性同步指定的API
-status            显示同步状态
-version           显示版本信息

# 示例
./api2mysql -config prod.yaml -log-level debug
./api2mysql -sync-once users
./api2mysql -status
```

## 日志

程序支持详细的日志记录：

- 支持不同日志级别：debug, info, warn, error
- 可输出到文件或控制台
- 包含时间戳、错误位置等详细信息

## 错误处理

- 内置重试机制，API请求失败时自动重试
- 详细的错误分类和错误码
- 完整的错误堆栈信息

## 性能优化

- 使用数据库连接池
- 批量插入数据
- 事务处理确保数据一致性

## 扩展新API

添加新的API接口非常简单，只需要在配置文件中添加新的API配置：

```yaml
apis:
  # 现有API配置...
  
  # 新增API配置
  - name: "products"
    description: "产品信息同步"
    url: "https://api.example.com/products"
    method: "GET"
    table_name: "products"
    field_mapping:
      id: "product_id"
      name: "product_name"
      price: "price"
    field_types:
      product_id: "INT PRIMARY KEY"
      product_name: "VARCHAR(200)"
      price: "DECIMAL(10,2)"
    sync_interval: 600
    enabled: true
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置信息
   - 确认数据库服务正在运行
   - 检查网络连接

2. **API请求失败**
   - 检查API地址是否正确
   - 验证认证信息
   - 检查网络连接

3. **数据同步失败**
   - 检查字段映射配置
   - 验证字段类型定义
   - 查看详细日志信息

### 调试模式

使用debug日志级别获取详细信息：

```bash
./api2mysql -log-level debug
```

## 许可证

MIT License
