# API到MySQL数据同步配置文件 - 演示配置

# MySQL数据库配置
database:
  host: "localhost"
  port: 3306
  username: "root"
  password: "password"
  database: "api_sync_demo"
  charset: "utf8mb4"
  max_open_conns: 10
  max_idle_conns: 5
  conn_max_lifetime: 300  # 秒

# API接口配置列表
apis:
  # JSONPlaceholder 用户信息接口（免费测试API）
  - name: "users"
    description: "用户信息同步 - JSONPlaceholder测试API"
    url: "https://jsonplaceholder.typicode.com/users"
    method: "GET"
    headers:
      Content-Type: "application/json"
    table_name: "demo_users"
    field_mapping:
      id: "id"
      name: "name"
      username: "username"
      email: "email"
      phone: "phone"
      website: "website"
      address.street: "address_street"
      address.suite: "address_suite"
      address.city: "address_city"
      address.zipcode: "address_zipcode"
      address.geo.lat: "address_lat"
      address.geo.lng: "address_lng"
      company.name: "company_name"
      company.catchPhrase: "company_catchphrase"
      company.bs: "company_bs"
    field_types:
      id: "INT PRIMARY KEY"
      name: "VARCHAR(100)"
      username: "VARCHAR(50)"
      email: "VARCHAR(100)"
      phone: "VARCHAR(50)"
      website: "VARCHAR(100)"
      address_street: "VARCHAR(100)"
      address_suite: "VARCHAR(50)"
      address_city: "VARCHAR(50)"
      address_zipcode: "VARCHAR(20)"
      address_lat: "DECIMAL(10,8)"
      address_lng: "DECIMAL(11,8)"
      company_name: "VARCHAR(100)"
      company_catchphrase: "VARCHAR(200)"
      company_bs: "VARCHAR(200)"
    sync_interval: 60  # 1分钟同步一次（演示用）
    enabled: true

  # JSONPlaceholder 文章信息接口
  - name: "posts"
    description: "文章信息同步 - JSONPlaceholder测试API"
    url: "https://jsonplaceholder.typicode.com/posts"
    method: "GET"
    headers:
      Content-Type: "application/json"
    table_name: "demo_posts"
    field_mapping:
      id: "id"
      userId: "user_id"
      title: "title"
      body: "content"
    field_types:
      id: "INT PRIMARY KEY"
      user_id: "INT"
      title: "VARCHAR(200)"
      content: "TEXT"
    sync_interval: 120  # 2分钟同步一次（演示用）
    enabled: true

  # JSONPlaceholder 评论信息接口
  - name: "comments"
    description: "评论信息同步 - JSONPlaceholder测试API"
    url: "https://jsonplaceholder.typicode.com/comments"
    method: "GET"
    headers:
      Content-Type: "application/json"
    table_name: "demo_comments"
    field_mapping:
      id: "id"
      postId: "post_id"
      name: "name"
      email: "email"
      body: "content"
    field_types:
      id: "INT PRIMARY KEY"
      post_id: "INT"
      name: "VARCHAR(100)"
      email: "VARCHAR(100)"
      content: "TEXT"
    sync_interval: 180  # 3分钟同步一次（演示用）
    enabled: false  # 默认禁用，可以手动启用

# 全局配置
global:
  # 日志级别: debug, info, warn, error
  log_level: "info"
  # 日志文件路径（可选，不设置则输出到控制台）
  log_file: "api2mysql-demo.log"
  # 同步模式: replace（替换）, append（追加）, upsert（更新插入）
  sync_mode: "replace"
  # 错误重试次数
  retry_count: 3
  # 请求超时时间（秒）
  request_timeout: 30
