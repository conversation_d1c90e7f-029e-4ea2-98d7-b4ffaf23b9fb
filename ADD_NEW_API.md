# 添加新API接口指南

## 快速添加新API的步骤

### 1. 获取API信息

首先需要获取以下信息：
- API地址（完整URL）
- 请求方法（通常是GET）
- 请求头（X-H3C-ID和X-H3C-APPKEY）
- API响应的数据结构

### 2. 使用自动化脚本添加（推荐）

```bash
# 使用脚本快速添加新API配置
./add_new_api.sh <api_name> <api_path> [table_name]

# 示例：添加学生成绩接口
./add_new_api.sh fdm_gxxs_xscjsj /api/v1/xs/fdm/fdm_gxxs_xscjsj student_scores
```

### 3. 手动添加API配置

如果不使用脚本，可以手动在 `config-production.yaml` 中添加：

```yaml
apis:
  # 新增API配置
  - name: "your_api_name"
    description: "API描述"
    url: "http://***********:33024/1033769420954/your/api/path"
    method: "GET"
    headers:
      X-H3C-ID: "1033769420954"
      X-H3C-APPKEY: "bt2qifdv"
      Content-Type: "application/json"
    params: {}
    table_name: "your_table_name"
    field_mapping:
      # API字段 -> 数据库字段
      api_field1: "db_field1"
      api_field2: "db_field2"
    field_types:
      # 数据库字段类型定义
      db_field1: "VARCHAR(50) PRIMARY KEY"
      db_field2: "VARCHAR(100)"
      created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    sync_interval: 3600
    enabled: true
```

### 4. 分析API响应结构

创建测试脚本来分析API响应：

```go
// test_your_api.go
package main

import (
	"fmt"
	"log"
	"time"
	"github.com/sirupsen/logrus"
	"api2mysql/api"
	"api2mysql/config"
)

func main() {
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)
	client := api.NewClient(60*time.Second, 3, logger)

	apiConfig := &config.APIConfig{
		Name:   "your_api_name",
		URL:    "http://***********:33024/1033769420954/your/api/path",
		Method: "GET",
		Headers: map[string]string{
			"X-H3C-ID":     "1033769420954",
			"X-H3C-APPKEY": "bt2qifdv",
			"Content-Type": "application/json",
		},
	}

	rawData, err := client.FetchData(apiConfig)
	if err != nil {
		log.Fatalf("获取API数据失败: %v", err)
	}

	fmt.Printf("成功获取 %d 条记录\n", len(rawData))
	// 分析数据结构...
}
```

### 5. 配置字段映射

根据API响应结构配置字段映射：

#### 简单字段映射
```yaml
field_mapping:
  id: "id"
  name: "name"
  code: "code"
```

#### 嵌套字段映射
```yaml
field_mapping:
  user.id: "user_id"
  user.name: "user_name"
  address.city: "city"
```

#### 字段类型定义
```yaml
field_types:
  id: "VARCHAR(50) PRIMARY KEY"    # 主键
  name: "VARCHAR(100) NOT NULL"    # 必填字符串
  code: "VARCHAR(20)"              # 可选字符串
  amount: "DECIMAL(10,2)"          # 金额
  count: "INT"                     # 整数
  is_active: "BOOLEAN"             # 布尔值
  created_date: "DATE"             # 日期
  created_time: "TIMESTAMP"        # 时间戳
  description: "TEXT"              # 长文本
```

### 6. 测试API配置

```bash
# 测试API数据获取
go run test_your_api.go

# 测试单次同步
./api2mysql -config config-production.yaml -sync-once your_api_name

# 查看同步状态
./api2mysql -config config-production.yaml -status
```

### 7. 启用持续同步

确认配置正确后，启用持续同步：

```bash
# 启动持续同步服务
./api2mysql -config config-production.yaml
```

## 已配置的API示例

### 1. 学生基本数据 (fdm_gxxs_xsjbsj)

```yaml
- name: "fdm_gxxs_xsjbsj"
  description: "学生基本数据子类表同步"
  url: "http://***********:33024/1033769420954/api/v1/xs/fdm/fdm_gxxs_xsjbsj"
  table_name: "fdm_gxxs_xsjbsj"
  field_mapping:
    xh: "student_number"        # 学号
    xm: "student_name"          # 姓名
    yxmc: "college_name"        # 学院名称
    zymc: "major_name"          # 专业名称
    bjmc: "class_name"          # 班级名称
```

### 2. 院系所单位信息 (fdm_xx_dw)

```yaml
- name: "fdm_xx_dw"
  description: "院系所单位基本信息同步"
  url: "http://***********:33024/1033769420954/data_center/xx/v1/fdm_xx_dw"
  table_name: "fdm_xx_dw"
  field_mapping:
    dwh: "department_code"      # 单位号
    dwmc: "department_name"     # 单位名称
    lsdwh: "parent_department_code"  # 隶属单位号
```

## 常见问题

### Q: 如何处理API返回的嵌套数据？
A: 使用点号分隔的字段路径，如 `user.profile.name: "user_name"`

### Q: 如何设置主键？
A: 在 `field_types` 中添加 `PRIMARY KEY`，如 `id: "VARCHAR(50) PRIMARY KEY"`

### Q: 如何处理日期字段？
A: 使用 `DATE` 或 `TIMESTAMP` 类型，程序会自动转换

### Q: 如何设置同步频率？
A: 修改 `sync_interval` 值（秒），如 3600 表示每小时同步一次

### Q: 如何临时禁用某个API？
A: 将 `enabled` 设置为 `false`

### Q: 如何查看同步日志？
A: 查看配置文件中 `global.log_file` 指定的日志文件

## 数据库表命名规范

建议使用以下命名规范：
- 表名：使用API名称或功能描述，如 `fdm_gxxs_xsjbsj`
- 字段名：使用下划线分隔的英文，如 `student_number`
- 主键：通常使用业务主键，如学号、部门代码等
- 时间戳：统一添加 `created_at` 和 `updated_at` 字段

## 性能优化建议

1. **合理设置同步间隔**：
   - 基础数据（如部门信息）：2-4小时
   - 业务数据（如学生信息）：1小时
   - 频繁变化数据：30分钟

2. **选择合适的同步模式**：
   - `replace`：适用于数据量小且需要完全一致的场景
   - `upsert`：适用于大部分业务场景（推荐）
   - `append`：适用于日志类只增不减的数据

3. **优化字段类型**：
   - 使用合适的字段长度，避免过长的VARCHAR
   - 数值类型优先使用INT而不是VARCHAR
   - 合理使用索引

4. **监控同步状态**：
   - 定期检查日志文件
   - 监控数据库表的记录数变化
   - 设置告警机制
