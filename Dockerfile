# 使用指定的 Alpine 基础镜像
FROM registry.cn-hangzhou.aliyuncs.com/yifangyun-library/alpine:3.22.1

# 设置维护者信息
LABEL maintainer="api2mysql"
LABEL description="API to MySQL data synchronization service"

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
RUN apk add --no-cache \
    ca-certificates \
    tzdata \
    && rm -rf /var/cache/apk/*

# 设置时区为中国时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 复制编译好的二进制文件
COPY api2mysql /app/api2mysql

# 复制配置文件
COPY config-production.yaml /app/config-production.yaml



# 创建日志目录
RUN mkdir -p /app/logs

# 设置文件权限
RUN chmod +x /app/api2mysql



# 暴露端口（如果应用有HTTP服务）
# EXPOSE 8080

# 设置环境变量
ENV CONFIG_FILE=/app/config-production.yaml
ENV LOG_LEVEL=info

# 健康检查（可选）
# HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
#   CMD /app/api2mysql -config $CONFIG_FILE -health-check || exit 1

# 启动命令
CMD ["/app/api2mysql", "-config", "/app/config-production.yaml", "-log-level", "info"]
