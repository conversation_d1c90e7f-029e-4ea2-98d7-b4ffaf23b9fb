# 使用指定的 Alpine 基础镜像
FROM registry.cn-hangzhou.aliyuncs.com/yifangyun-library/alpine:3.22.1

# 设置维护者信息
LABEL maintainer="api2mysql"
LABEL description="API to MySQL data synchronization service"

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
RUN apk add --no-cache \
    ca-certificates \
    tzdata \
    gettext \
    && rm -rf /var/cache/apk/*

# 设置时区为中国时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 复制编译好的二进制文件
COPY api2mysql /app/api2mysql

# 复制配置文件模板
COPY config-production.yaml /app/config-template.yaml

# 创建配置文件生成脚本
RUN cat > /app/generate-config.sh << 'EOF'
#!/bin/sh

# 设置默认值
export DB_HOST=${DB_HOST:-"localhost"}
export DB_PORT=${DB_PORT:-"3306"}
export DB_USERNAME=${DB_USERNAME:-"root"}
export DB_PASSWORD=${DB_PASSWORD:-""}
export DB_DATABASE=${DB_DATABASE:-"api2mysql"}
export DB_CHARSET=${DB_CHARSET:-"utf8mb4"}
export DB_MAX_OPEN_CONNS=${DB_MAX_OPEN_CONNS:-"10"}
export DB_MAX_IDLE_CONNS=${DB_MAX_IDLE_CONNS:-"5"}
export DB_CONN_MAX_LIFETIME=${DB_CONN_MAX_LIFETIME:-"300"}

export LOG_LEVEL=${LOG_LEVEL:-"info"}
export LOG_FILE=${LOG_FILE:-"/app/logs/data_sync.log"}
export SYNC_MODE=${SYNC_MODE:-"replace"}
export RETRY_COUNT=${RETRY_COUNT:-"3"}
export REQUEST_TIMEOUT=${REQUEST_TIMEOUT:-"30"}

# 使用 envsubst 替换模板中的环境变量
envsubst < /app/config-template.yaml > /app/config-production.yaml

echo "配置文件已生成，数据库配置:"
echo "  主机: $DB_HOST:$DB_PORT"
echo "  用户: $DB_USERNAME"
echo "  数据库: $DB_DATABASE"
echo "  日志级别: $LOG_LEVEL"
EOF

# 设置脚本权限
RUN chmod +x /app/generate-config.sh

# 创建日志目录
RUN mkdir -p /app/logs

# 设置文件权限
RUN chmod +x /app/api2mysql

# 设置默认环境变量
ENV DB_HOST=localhost
ENV DB_PORT=3306
ENV DB_USERNAME=root
ENV DB_PASSWORD=""
ENV DB_DATABASE=api2mysql
ENV DB_CHARSET=utf8mb4
ENV DB_MAX_OPEN_CONNS=10
ENV DB_MAX_IDLE_CONNS=5
ENV DB_CONN_MAX_LIFETIME=300
ENV LOG_LEVEL=info
ENV LOG_FILE=/app/logs/data_sync.log
ENV SYNC_MODE=replace
ENV RETRY_COUNT=3
ENV REQUEST_TIMEOUT=30

# 健康检查（可选）
# HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
#   CMD /app/api2mysql -config /app/config-production.yaml -health-check || exit 1

# 启动命令：先生成配置文件，然后启动应用
CMD ["/bin/sh", "-c", "/app/generate-config.sh && /app/api2mysql -config /app/config-production.yaml -log-level $LOG_LEVEL"]
