# 使用指定的 Alpine 基础镜像
FROM registry.cn-hangzhou.aliyuncs.com/yifangyun-library/alpine:3.22.1

# 设置维护者信息
LABEL maintainer="api2mysql"
LABEL description="API to MySQL data synchronization service"

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
RUN apk add --no-cache \
    ca-certificates \
    tzdata \
    && rm -rf /var/cache/apk/*

# 设置时区为中国时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 复制编译好的二进制文件
COPY api2mysql /app/api2mysql

# 复制默认配置文件（可被外部挂载覆盖）
COPY config-production.yaml /app/config-production.yaml

# 创建配置和日志目录
RUN mkdir -p /app/config /app/logs

# 设置文件权限
RUN chmod +x /app/api2mysql

# 创建启动脚本
RUN cat > /app/start.sh << 'EOF'
#!/bin/sh

# 检查是否有外部挂载的配置文件
if [ -f "/app/config/config-production.yaml" ]; then
    echo "使用外部挂载的配置文件: /app/config/config-production.yaml"
    CONFIG_FILE="/app/config/config-production.yaml"
else
    echo "使用默认配置文件: /app/config-production.yaml"
    CONFIG_FILE="/app/config-production.yaml"
fi

# 显示配置文件信息
echo "配置文件路径: $CONFIG_FILE"
echo "日志级别: ${LOG_LEVEL:-info}"

# 启动应用
exec /app/api2mysql -config "$CONFIG_FILE" -log-level "${LOG_LEVEL:-info}"
EOF

# 设置启动脚本权限
RUN chmod +x /app/start.sh

# 设置环境变量
ENV LOG_LEVEL=info

# 暴露端口（如果应用有HTTP服务）
# EXPOSE 8080

# 创建挂载点
VOLUME ["/app/config", "/app/logs"]

# 健康检查（可选）
# HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
#   CMD /app/api2mysql -config /app/config-production.yaml -health-check || exit 1

# 启动命令
CMD ["/app/start.sh"]
