# Git 相关
.git
.gitignore
.gitattributes

# 文档
README.md
*.md
docs/

# 开发工具配置
.vscode/
.idea/
*.swp
*.swo
*~

# 测试文件
*_test.go
test_*.go
**/test/
**/tests/

# 构建产物
*.exe
*.dll
*.so
*.dylib

# 日志文件
*.log
logs/
data_sync.log

# 临时文件
*.tmp
*.temp
.DS_Store
Thumbs.db

# Go 相关
vendor/
go.sum
go.mod

# 配置文件（保留生产配置）
config-dev.yaml
config-test.yaml

# Docker 相关
Dockerfile*
.dockerignore
docker-compose*.yml

# CI/CD
.github/
.gitlab-ci.yml
Jenkinsfile

# 其他
*.bak
*.orig
