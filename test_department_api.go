package main

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/sirupsen/logrus"

	"api2mysql/api"
	"api2mysql/config"
)

func main() {
	// 创建日志器
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// 创建API客户端
	client := api.NewClient(60*time.Second, 3, logger)

	// H3C 院系所单位基本信息API配置
	apiConfig := &config.APIConfig{
		Name:   "fdm_xx_dw",
		URL:    "http://10.89.10.81:33024/1033769420954/data_center/xx/v1/fdm_xx_dw",
		Method: "GET",
		Headers: map[string]string{
			"X-H3C-ID":     "1033769420954",
			"X-H3C-APPKEY": "bt2qifdv",
			"Content-Type": "application/json",
		},
		FieldMapping: map[string]string{
			"dwh":       "department_code",
			"dwmc":      "department_name",
			"dwywmc":    "department_english_name",
			"dwjc":      "department_short_name",
			"lsdwh":     "parent_department_code",
			"lsdwh_mc":  "parent_department_name",
			"dwfzrh":    "department_head_code",
			"dwfzrh_mc": "department_head_name",
			"sxrq":      "effective_date",
			"dwyxbs":    "department_validity_flag",
		},
	}

	fmt.Println("测试H3C院系所单位基本信息API数据获取...")
	fmt.Printf("API地址: %s\n", apiConfig.URL)
	fmt.Printf("请求头: %+v\n\n", apiConfig.Headers)

	// 获取原始数据
	rawData, err := client.FetchData(apiConfig)
	if err != nil {
		log.Fatalf("获取API数据失败: %v", err)
	}

	fmt.Printf("✅ 成功获取 %d 条原始记录\n\n", len(rawData))

	// 显示原始数据结构（前3条记录）
	fmt.Println("📋 原始数据结构分析:")
	for i, record := range rawData {
		if i >= 3 { // 只显示前3条
			break
		}
		
		fmt.Printf("\n--- 记录 %d ---\n", i+1)
		
		// 美化JSON输出
		jsonData, err := json.MarshalIndent(record, "", "  ")
		if err != nil {
			fmt.Printf("JSON序列化失败: %v\n", err)
			continue
		}
		
		fmt.Println(string(jsonData))
	}

	// 测试数据转换
	if len(rawData) > 0 {
		fmt.Println("\n🔄 测试数据转换...")
		transformedData, err := client.TransformData(rawData, apiConfig.FieldMapping)
		if err != nil {
			log.Printf("数据转换失败: %v", err)
		} else {
			fmt.Printf("✅ 成功转换 %d 条记录\n", len(transformedData))
			
			// 显示转换后的数据示例
			fmt.Println("\n📋 转换后的数据示例:")
			for i, record := range transformedData {
				if i >= 3 { // 只显示前3条
					break
				}
				fmt.Printf("\n--- 转换后记录 %d ---\n", i+1)
				for key, value := range record {
					fmt.Printf("  %s: %v\n", key, value)
				}
			}
			
			// 分析部门层级结构
			fmt.Println("\n🏢 部门层级结构分析:")
			analyzeDepartmentHierarchy(transformedData)
		}
	}

	fmt.Println("\n🎉 院系所单位信息API测试完成！")
	fmt.Println("\n📝 下一步:")
	fmt.Println("1. 配置文件已更新，字段映射已配置完成")
	fmt.Println("2. 设置正确的数据库连接信息")
	fmt.Println("3. 测试同步: ./api2mysql -config config-production.yaml -sync-once fdm_xx_dw")
	fmt.Println("4. 启动持续同步: ./api2mysql -config config-production.yaml")
}

// analyzeDepartmentHierarchy 分析部门层级结构
func analyzeDepartmentHierarchy(data []map[string]interface{}) {
	// 统计根部门（没有上级部门的）
	var rootDepartments []map[string]interface{}
	var subDepartments []map[string]interface{}
	
	for _, dept := range data {
		parentCode := dept["parent_department_code"]
		if parentCode == nil || parentCode == "" || parentCode == "0" {
			rootDepartments = append(rootDepartments, dept)
		} else {
			subDepartments = append(subDepartments, dept)
		}
	}
	
	fmt.Printf("  📊 统计信息:\n")
	fmt.Printf("    - 总部门数: %d\n", len(data))
	fmt.Printf("    - 根部门数: %d\n", len(rootDepartments))
	fmt.Printf("    - 子部门数: %d\n", len(subDepartments))
	
	fmt.Printf("\n  🏛️ 根部门列表:\n")
	for i, dept := range rootDepartments {
		if i >= 5 { // 只显示前5个
			fmt.Printf("    ... 还有 %d 个根部门\n", len(rootDepartments)-5)
			break
		}
		fmt.Printf("    - [%v] %v\n", dept["department_code"], dept["department_name"])
	}
	
	// 分析部门层级深度
	maxDepth := calculateMaxDepth(data)
	fmt.Printf("\n  📏 最大层级深度: %d\n", maxDepth)
	
	// 显示一些子部门示例
	if len(subDepartments) > 0 {
		fmt.Printf("\n  🏢 子部门示例:\n")
		for i, dept := range subDepartments {
			if i >= 3 { // 只显示前3个
				break
			}
			fmt.Printf("    - [%v] %v (隶属于: %v)\n", 
				dept["department_code"], 
				dept["department_name"], 
				dept["parent_department_name"])
		}
	}
}

// calculateMaxDepth 计算部门层级的最大深度
func calculateMaxDepth(data []map[string]interface{}) int {
	// 构建部门映射
	deptMap := make(map[string]map[string]interface{})
	for _, dept := range data {
		if code, ok := dept["department_code"].(string); ok {
			deptMap[code] = dept
		}
	}
	
	maxDepth := 0
	
	// 对每个部门计算其深度
	for _, dept := range data {
		depth := calculateDeptDepth(dept, deptMap, 0)
		if depth > maxDepth {
			maxDepth = depth
		}
	}
	
	return maxDepth
}

// calculateDeptDepth 递归计算单个部门的深度
func calculateDeptDepth(dept map[string]interface{}, deptMap map[string]map[string]interface{}, currentDepth int) int {
	// 防止无限递归
	if currentDepth > 10 {
		return currentDepth
	}
	
	parentCode := dept["parent_department_code"]
	if parentCode == nil || parentCode == "" || parentCode == "0" {
		return currentDepth + 1
	}
	
	if parentCodeStr, ok := parentCode.(string); ok {
		if parent, exists := deptMap[parentCodeStr]; exists {
			return calculateDeptDepth(parent, deptMap, currentDepth+1)
		}
	}
	
	return currentDepth + 1
}
