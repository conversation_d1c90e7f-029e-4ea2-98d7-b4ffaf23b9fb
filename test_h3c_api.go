package main

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/sirupsen/logrus"

	"api2mysql/api"
	"api2mysql/config"
)

func main() {
	// 创建日志器
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// 创建API客户端
	client := api.NewClient(60*time.Second, 3, logger)

	// H3C API配置
	apiConfig := &config.APIConfig{
		Name:   "fdm_gxxs_xsjbsj",
		URL:    "http://10.89.10.81:33024/1033769420954/api/v1/xs/fdm/fdm_gxxs_xsjbsj",
		Method: "GET",
		Headers: map[string]string{
			"X-H3C-ID":     "1033769420954",
			"X-H3C-APPKEY": "bt2qifdv",
			"Content-Type": "application/json",
		},
		FieldMapping: map[string]string{
			"xh":      "student_number",
			"xm":      "student_name",
			"ywxm":    "english_name",
			"xmpy":    "name_pinyin",
			"yxdm":    "college_code",
			"yxmc":    "college_name",
			"zydm":    "major_code",
			"zymc":    "major_name",
			"bjdm":    "class_code",
			"bjmc":    "class_name",
			"xsdqztm": "student_status_code",
			"xsdqzt":  "student_status",
			"dzxx":    "email",
			"xjh":     "student_id_number",
		},
	}

	fmt.Println("测试H3C API数据获取...")
	fmt.Printf("API地址: %s\n", apiConfig.URL)
	fmt.Printf("请求头: %+v\n\n", apiConfig.Headers)

	// 获取原始数据
	rawData, err := client.FetchData(apiConfig)
	if err != nil {
		log.Fatalf("获取API数据失败: %v", err)
	}

	fmt.Printf("✅ 成功获取 %d 条原始记录\n\n", len(rawData))

	// 显示原始数据结构（前2条记录）
	fmt.Println("📋 原始数据结构分析:")
	for i, record := range rawData {
		if i >= 2 { // 只显示前2条
			break
		}

		fmt.Printf("\n--- 记录 %d ---\n", i+1)

		// 美化JSON输出
		jsonData, err := json.MarshalIndent(record, "", "  ")
		if err != nil {
			fmt.Printf("JSON序列化失败: %v\n", err)
			continue
		}

		fmt.Println(string(jsonData))

		// 分析字段结构
		if recordMap, ok := record.(map[string]interface{}); ok {
			fmt.Printf("\n🔍 字段分析:\n")
			analyzeFields(recordMap, "")
		}
	}

	// 测试数据转换
	if len(rawData) > 0 {
		fmt.Println("\n🔄 测试数据转换...")
		transformedData, err := client.TransformData(rawData, apiConfig.FieldMapping)
		if err != nil {
			log.Printf("数据转换失败: %v", err)
		} else {
			fmt.Printf("✅ 成功转换 %d 条记录\n", len(transformedData))

			// 显示转换后的数据示例
			fmt.Println("\n📋 转换后的数据示例:")
			for i, record := range transformedData {
				if i >= 2 { // 只显示前2条
					break
				}
				fmt.Printf("\n--- 转换后记录 %d ---\n", i+1)
				for key, value := range record {
					fmt.Printf("  %s: %v\n", key, value)
				}
			}
		}
	}

	fmt.Println("\n🎉 API测试完成！")
	fmt.Println("\n📝 下一步:")
	fmt.Println("1. 配置文件已更新，字段映射已配置完成")
	fmt.Println("2. 设置正确的数据库连接信息")
	fmt.Println("3. 测试同步: ./api2mysql -config config-production.yaml -sync-once fdm_gxxs_xsjbsj")
	fmt.Println("4. 启动持续同步: ./api2mysql -config config-production.yaml")
}

// analyzeFields 分析字段结构
func analyzeFields(data map[string]interface{}, prefix string) {
	for key, value := range data {
		fullKey := key
		if prefix != "" {
			fullKey = prefix + "." + key
		}

		switch v := value.(type) {
		case map[string]interface{}:
			fmt.Printf("  %s: 对象 (嵌套字段)\n", fullKey)
			analyzeFields(v, fullKey)
		case []interface{}:
			fmt.Printf("  %s: 数组 (长度: %d)\n", fullKey, len(v))
			if len(v) > 0 {
				if firstItem, ok := v[0].(map[string]interface{}); ok {
					analyzeFields(firstItem, fullKey+"[0]")
				}
			}
		case string:
			fmt.Printf("  %s: 字符串 (\"%s\")\n", fullKey, v)
		case float64:
			fmt.Printf("  %s: 数字 (%.2f)\n", fullKey, v)
		case bool:
			fmt.Printf("  %s: 布尔值 (%t)\n", fullKey, v)
		case nil:
			fmt.Printf("  %s: 空值\n", fullKey)
		default:
			fmt.Printf("  %s: 其他类型 (%T)\n", fullKey, v)
		}
	}
}

// generateFieldMappingSuggestion 生成字段映射建议
func generateFieldMappingSuggestion(record interface{}) {
	if recordMap, ok := record.(map[string]interface{}); ok {
		fmt.Println("\n# 建议的字段映射配置 (field_mapping):")
		fmt.Println("field_mapping:")

		var fieldTypes []string
		fmt.Println("\n# 建议的字段类型配置 (field_types):")
		fmt.Println("field_types:")

		generateMappingForFields(recordMap, "", &fieldTypes)

		for _, fieldType := range fieldTypes {
			fmt.Println(fieldType)
		}

		// 添加标准的时间戳字段
		fmt.Println("  created_at: \"TIMESTAMP DEFAULT CURRENT_TIMESTAMP\"")
		fmt.Println("  updated_at: \"TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\"")
	}
}

// generateMappingForFields 为字段生成映射建议
func generateMappingForFields(data map[string]interface{}, prefix string, fieldTypes *[]string) {
	for key, value := range data {
		fullKey := key
		dbField := convertToDBFieldName(key)

		if prefix != "" {
			fullKey = prefix + "." + key
			dbField = convertToDBFieldName(prefix) + "_" + convertToDBFieldName(key)
		}

		switch v := value.(type) {
		case map[string]interface{}:
			// 递归处理嵌套对象
			generateMappingForFields(v, fullKey, fieldTypes)
		case []interface{}:
			// 数组字段，通常转换为JSON字符串存储
			fmt.Printf("  %s: \"%s\"  # 数组字段\n", fullKey, dbField)
			*fieldTypes = append(*fieldTypes, fmt.Sprintf("  %s: \"JSON\"", dbField))
		case string:
			fmt.Printf("  %s: \"%s\"\n", fullKey, dbField)
			// 根据字段名推测类型
			if isDateField(key) {
				*fieldTypes = append(*fieldTypes, fmt.Sprintf("  %s: \"DATE\"", dbField))
			} else if isIDField(key) {
				*fieldTypes = append(*fieldTypes, fmt.Sprintf("  %s: \"VARCHAR(50)\"", dbField))
			} else {
				*fieldTypes = append(*fieldTypes, fmt.Sprintf("  %s: \"VARCHAR(255)\"", dbField))
			}
		case float64:
			fmt.Printf("  %s: \"%s\"\n", fullKey, dbField)
			if isIntegerValue(v) {
				*fieldTypes = append(*fieldTypes, fmt.Sprintf("  %s: \"INT\"", dbField))
			} else {
				*fieldTypes = append(*fieldTypes, fmt.Sprintf("  %s: \"DECIMAL(10,2)\"", dbField))
			}
		case bool:
			fmt.Printf("  %s: \"%s\"\n", fullKey, dbField)
			*fieldTypes = append(*fieldTypes, fmt.Sprintf("  %s: \"BOOLEAN\"", dbField))
		default:
			fmt.Printf("  %s: \"%s\"  # %T\n", fullKey, dbField, v)
			*fieldTypes = append(*fieldTypes, fmt.Sprintf("  %s: \"TEXT\"", dbField))
		}
	}
}

// convertToDBFieldName 将API字段名转换为数据库字段名
func convertToDBFieldName(fieldName string) string {
	// 这里可以根据需要添加更多转换规则
	// 例如：驼峰转下划线等
	return fieldName
}

// isDateField 判断是否为日期字段
func isDateField(fieldName string) bool {
	dateFields := []string{"date", "time", "rq", "sj", "csrq", "rxrq"}
	for _, df := range dateFields {
		if fieldName == df || len(fieldName) >= len(df) && fieldName[len(fieldName)-len(df):] == df {
			return true
		}
	}
	return false
}

// isIDField 判断是否为ID字段
func isIDField(fieldName string) bool {
	return fieldName == "id" || fieldName == "ID" ||
		len(fieldName) >= 2 && fieldName[len(fieldName)-2:] == "id" ||
		len(fieldName) >= 2 && fieldName[len(fieldName)-2:] == "ID"
}

// isIntegerValue 判断浮点数是否为整数值
func isIntegerValue(f float64) bool {
	return f == float64(int64(f))
}
