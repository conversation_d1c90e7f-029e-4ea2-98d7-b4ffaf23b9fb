# API2MySQL 使用指南

## 项目概述

API2MySQL 是一个用Go语言开发的数据同步工具，可以：

- 从配置的API接口自动获取数据
- 将数据同步到MySQL数据库
- 支持多个API接口，每个接口对应一个数据库表
- 通过配置文件管理所有设置，无需修改代码
- 支持字段映射、数据转换、定时同步等功能

## 快速开始

### 1. 编译程序

```bash
go build -o api2mysql
```

### 2. 查看帮助信息

```bash
./api2mysql -h
```

### 3. 查看当前配置状态

```bash
./api2mysql -status
```

### 4. 测试API数据获取（无需数据库）

```bash
go run test_api.go
```

## 配置文件详解

### 基本结构

配置文件使用YAML格式，包含三个主要部分：

1. **database**: 数据库连接配置
2. **apis**: API接口配置列表
3. **global**: 全局设置

### 数据库配置示例

```yaml
database:
  host: "localhost"
  port: 3306
  username: "root"
  password: "your_password"
  database: "api_sync"
  charset: "utf8mb4"
  max_open_conns: 10
  max_idle_conns: 5
  conn_max_lifetime: 300
```

### API配置示例

```yaml
apis:
  - name: "users"                    # 唯一标识符
    description: "用户信息同步"        # 描述信息
    url: "https://api.example.com/users"  # API地址
    method: "GET"                    # HTTP方法
    headers:                         # 请求头
      Content-Type: "application/json"
      Authorization: "Bearer your-token"
    params:                          # 查询参数（可选）
      page: "1"
      limit: "100"
    table_name: "users"              # 数据库表名
    field_mapping:                   # 字段映射
      id: "user_id"                  # API字段 -> 数据库字段
      name: "user_name"
      address.city: "city"           # 支持嵌套字段
    field_types:                     # 数据库字段类型
      user_id: "INT PRIMARY KEY"
      user_name: "VARCHAR(100)"
      city: "VARCHAR(50)"
    sync_interval: 300               # 同步间隔（秒）
    enabled: true                    # 是否启用
```

## 运行模式

### 1. 持续同步模式（默认）

```bash
./api2mysql
```

程序会根据配置文件中的 `sync_interval` 定时同步所有启用的API。

### 2. 一次性同步模式

```bash
./api2mysql -sync-once users
```

只执行指定API的一次同步，适用于测试或手动触发。

### 3. 状态查看模式

```bash
./api2mysql -status
```

显示当前配置的同步状态，不需要数据库连接。

## 同步模式说明

在 `global.sync_mode` 中配置：

### replace（替换模式）
- 每次同步前清空表中所有数据
- 然后插入新获取的数据
- 适用于数据量不大且需要保持与API完全一致的场景

### append（追加模式）
- 直接将新数据插入表中
- 不删除原有数据
- 适用于日志类数据或只增不减的数据

### upsert（更新插入模式）
- 如果记录存在（根据主键判断）则更新
- 如果记录不存在则插入
- 需要在 `field_types` 中定义主键
- 适用于需要保持数据最新状态的场景

## 字段映射功能

### 简单字段映射

```yaml
field_mapping:
  id: "user_id"        # API的id字段 -> 数据库的user_id字段
  name: "user_name"    # API的name字段 -> 数据库的user_name字段
```

### 嵌套字段映射

```yaml
field_mapping:
  address.street: "street"           # API的address.street -> 数据库的street
  address.city: "city"               # API的address.city -> 数据库的city
  profile.avatar.url: "avatar_url"   # 支持多层嵌套
```

## 数据库表管理

### 自动建表

程序会根据 `field_types` 配置自动创建表：

```yaml
field_types:
  id: "INT PRIMARY KEY"              # 主键
  name: "VARCHAR(100)"               # 字符串
  age: "INT"                         # 整数
  salary: "DECIMAL(10,2)"            # 小数
  created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"  # 时间戳
  description: "TEXT"                # 长文本
```

### 支持的字段类型

- `INT`, `BIGINT`, `SMALLINT` - 整数类型
- `VARCHAR(n)` - 变长字符串
- `CHAR(n)` - 定长字符串
- `TEXT` - 长文本
- `DECIMAL(m,n)` - 精确小数
- `FLOAT`, `DOUBLE` - 浮点数
- `TIMESTAMP`, `DATETIME`, `DATE` - 时间类型
- `PRIMARY KEY` - 主键约束

## 错误处理和重试

### 自动重试

程序内置重试机制：

```yaml
global:
  retry_count: 3        # 失败后重试3次
  request_timeout: 30   # 每次请求超时30秒
```

### 错误日志

所有错误都会记录到日志文件中：

```yaml
global:
  log_level: "info"           # 日志级别
  log_file: "api2mysql.log"   # 日志文件路径
```

## 实际使用示例

### 示例1：同步用户数据

```yaml
apis:
  - name: "users"
    url: "https://your-api.com/users"
    method: "GET"
    headers:
      Authorization: "Bearer your-api-token"
    table_name: "users"
    field_mapping:
      id: "id"
      name: "name"
      email: "email"
      created_at: "created_at"
    field_types:
      id: "INT PRIMARY KEY"
      name: "VARCHAR(100)"
      email: "VARCHAR(100)"
      created_at: "TIMESTAMP"
    sync_interval: 3600  # 每小时同步一次
    enabled: true
```

### 示例2：同步订单数据

```yaml
apis:
  - name: "orders"
    url: "https://your-api.com/orders"
    method: "GET"
    params:
      status: "completed"
      limit: "1000"
    table_name: "orders"
    field_mapping:
      id: "order_id"
      customer.id: "customer_id"
      customer.name: "customer_name"
      total: "total_amount"
      status: "status"
    field_types:
      order_id: "INT PRIMARY KEY"
      customer_id: "INT"
      customer_name: "VARCHAR(100)"
      total_amount: "DECIMAL(10,2)"
      status: "VARCHAR(20)"
    sync_interval: 1800  # 每30分钟同步一次
    enabled: true
```

## 部署建议

### 1. 生产环境配置

```yaml
global:
  log_level: "warn"              # 生产环境使用warn级别
  log_file: "/var/log/api2mysql.log"
  sync_mode: "upsert"            # 推荐使用upsert模式
  retry_count: 5                 # 增加重试次数
  request_timeout: 60            # 增加超时时间

database:
  max_open_conns: 20             # 增加连接池大小
  max_idle_conns: 10
  conn_max_lifetime: 3600        # 增加连接生存时间
```

### 2. 使用systemd管理服务

创建 `/etc/systemd/system/api2mysql.service`：

```ini
[Unit]
Description=API2MySQL Data Sync Service
After=network.target mysql.service

[Service]
Type=simple
User=api2mysql
WorkingDirectory=/opt/api2mysql
ExecStart=/opt/api2mysql/api2mysql -config /opt/api2mysql/config.yaml
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### 3. 监控和告警

- 监控日志文件中的ERROR级别日志
- 设置数据库连接监控
- 监控同步间隔是否正常

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否运行
   - 验证连接参数是否正确
   - 检查防火墙设置

2. **API请求失败**
   - 验证API地址是否正确
   - 检查认证信息是否有效
   - 确认网络连接正常

3. **数据同步异常**
   - 检查字段映射配置
   - 验证字段类型定义
   - 查看详细日志信息

### 调试技巧

```bash
# 使用debug级别查看详细日志
./api2mysql -log-level debug

# 测试单个API
./api2mysql -sync-once api_name

# 验证配置文件
./api2mysql -status
```
