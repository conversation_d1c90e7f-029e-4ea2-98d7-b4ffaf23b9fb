# API到MySQL数据同步配置文件

# MySQL数据库配置
database:
  host: "localhost"
  port: 3306
  username: "root"
  password: "password"
  database: "api_sync"
  charset: "utf8mb4"
  max_open_conns: 10
  max_idle_conns: 5
  conn_max_lifetime: 300  # 秒

# API接口配置列表
apis:
  # 用户信息接口示例
  - name: "users"
    description: "用户信息同步"
    url: "https://jsonplaceholder.typicode.com/users"
    method: "GET"
    headers:
      Content-Type: "application/json"
      # Authorization: "Bearer your-token-here"
    # 请求参数（可选）
    params:
      # page: "1"
      # limit: "100"
    # 请求体（可选，用于POST/PUT请求）
    body: {}
    # 对应的MySQL表名
    table_name: "users"
    # 字段映射配置（API字段 -> 数据库字段）
    field_mapping:
      id: "id"
      name: "name"
      username: "username"
      email: "email"
      phone: "phone"
      website: "website"
      address.street: "address_street"
      address.suite: "address_suite"
      address.city: "address_city"
      address.zipcode: "address_zipcode"
      address.geo.lat: "address_lat"
      address.geo.lng: "address_lng"
      company.name: "company_name"
      company.catchPhrase: "company_catchphrase"
      company.bs: "company_bs"
    # 字段类型定义（用于创建表）
    field_types:
      id: "INT PRIMARY KEY"
      name: "VARCHAR(100)"
      username: "VARCHAR(50)"
      email: "VARCHAR(100)"
      phone: "VARCHAR(50)"
      website: "VARCHAR(100)"
      address_street: "VARCHAR(100)"
      address_suite: "VARCHAR(50)"
      address_city: "VARCHAR(50)"
      address_zipcode: "VARCHAR(20)"
      address_lat: "DECIMAL(10,8)"
      address_lng: "DECIMAL(11,8)"
      company_name: "VARCHAR(100)"
      company_catchphrase: "VARCHAR(200)"
      company_bs: "VARCHAR(200)"
    # 同步间隔（秒）
    sync_interval: 300
    # 是否启用
    enabled: true

  # 文章信息接口示例
  - name: "posts"
    description: "文章信息同步"
    url: "https://jsonplaceholder.typicode.com/posts"
    method: "GET"
    headers:
      Content-Type: "application/json"
    table_name: "posts"
    field_mapping:
      id: "id"
      userId: "user_id"
      title: "title"
      body: "content"
    field_types:
      id: "INT PRIMARY KEY"
      user_id: "INT"
      title: "VARCHAR(200)"
      content: "TEXT"
    sync_interval: 600
    enabled: true

# 全局配置
global:
  # 日志级别: debug, info, warn, error
  log_level: "info"
  # 日志文件路径（可选，不设置则输出到控制台）
  log_file: "api2mysql.log"
  # 同步模式: replace（替换）, append（追加）, upsert（更新插入）
  sync_mode: "replace"
  # 错误重试次数
  retry_count: 3
  # 请求超时时间（秒）
  request_timeout: 30
