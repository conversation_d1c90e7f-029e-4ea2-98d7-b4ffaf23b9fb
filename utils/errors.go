package utils

import (
	"fmt"
	"runtime"
	"strings"
)

// ErrorType 错误类型
type ErrorType string

const (
	ErrorTypeConfig   ErrorType = "CONFIG"
	ErrorTypeDatabase ErrorType = "DATABASE"
	ErrorTypeAPI      ErrorType = "API"
	ErrorTypeSync     ErrorType = "SYNC"
	ErrorTypeNetwork  ErrorType = "NETWORK"
	ErrorTypeValidation ErrorType = "VALIDATION"
)

// AppError 应用错误
type AppError struct {
	Type     ErrorType `json:"type"`
	Code     string    `json:"code"`
	Message  string    `json:"message"`
	Details  string    `json:"details,omitempty"`
	Cause    error     `json:"cause,omitempty"`
	Location string    `json:"location,omitempty"`
}

// Error 实现error接口
func (e *AppError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("[%s:%s] %s - %s", e.Type, e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("[%s:%s] %s", e.Type, e.Code, e.Message)
}

// Unwrap 实现errors.Unwrap接口
func (e *AppError) Unwrap() error {
	return e.Cause
}

// NewError 创建新的应用错误
func NewError(errorType ErrorType, code, message string) *AppError {
	return &AppError{
		Type:     errorType,
		Code:     code,
		Message:  message,
		Location: getCallerLocation(),
	}
}

// NewErrorWithDetails 创建带详细信息的应用错误
func NewErrorWithDetails(errorType ErrorType, code, message, details string) *AppError {
	return &AppError{
		Type:     errorType,
		Code:     code,
		Message:  message,
		Details:  details,
		Location: getCallerLocation(),
	}
}

// NewErrorWithCause 创建带原因的应用错误
func NewErrorWithCause(errorType ErrorType, code, message string, cause error) *AppError {
	return &AppError{
		Type:     errorType,
		Code:     code,
		Message:  message,
		Cause:    cause,
		Location: getCallerLocation(),
	}
}

// WrapError 包装现有错误
func WrapError(errorType ErrorType, code, message string, cause error) *AppError {
	return &AppError{
		Type:     errorType,
		Code:     code,
		Message:  message,
		Cause:    cause,
		Location: getCallerLocation(),
	}
}

// getCallerLocation 获取调用者位置信息
func getCallerLocation() string {
	_, file, line, ok := runtime.Caller(2)
	if !ok {
		return "unknown"
	}
	
	// 只保留文件名，不包含完整路径
	parts := strings.Split(file, "/")
	if len(parts) > 0 {
		file = parts[len(parts)-1]
	}
	
	return fmt.Sprintf("%s:%d", file, line)
}

// 预定义的错误代码
const (
	// 配置错误
	ErrCodeConfigNotFound     = "CONFIG_NOT_FOUND"
	ErrCodeConfigInvalid      = "CONFIG_INVALID"
	ErrCodeConfigParseFailed  = "CONFIG_PARSE_FAILED"
	
	// 数据库错误
	ErrCodeDBConnectionFailed = "DB_CONNECTION_FAILED"
	ErrCodeDBQueryFailed      = "DB_QUERY_FAILED"
	ErrCodeDBTransactionFailed = "DB_TRANSACTION_FAILED"
	ErrCodeTableCreateFailed  = "TABLE_CREATE_FAILED"
	ErrCodeDataInsertFailed   = "DATA_INSERT_FAILED"
	
	// API错误
	ErrCodeAPIRequestFailed   = "API_REQUEST_FAILED"
	ErrCodeAPIResponseInvalid = "API_RESPONSE_INVALID"
	ErrCodeAPITimeout         = "API_TIMEOUT"
	ErrCodeAPIAuthFailed      = "API_AUTH_FAILED"
	
	// 同步错误
	ErrCodeSyncFailed         = "SYNC_FAILED"
	ErrCodeDataTransformFailed = "DATA_TRANSFORM_FAILED"
	ErrCodeFieldMappingFailed = "FIELD_MAPPING_FAILED"
	
	// 网络错误
	ErrCodeNetworkTimeout     = "NETWORK_TIMEOUT"
	ErrCodeNetworkUnavailable = "NETWORK_UNAVAILABLE"
	
	// 验证错误
	ErrCodeValidationFailed   = "VALIDATION_FAILED"
	ErrCodeRequiredFieldMissing = "REQUIRED_FIELD_MISSING"
)

// 预定义的常用错误创建函数
func NewConfigError(code, message string) *AppError {
	return NewError(ErrorTypeConfig, code, message)
}

func NewDatabaseError(code, message string) *AppError {
	return NewError(ErrorTypeDatabase, code, message)
}

func NewAPIError(code, message string) *AppError {
	return NewError(ErrorTypeAPI, code, message)
}

func NewSyncError(code, message string) *AppError {
	return NewError(ErrorTypeSync, code, message)
}

func NewNetworkError(code, message string) *AppError {
	return NewError(ErrorTypeNetwork, code, message)
}

func NewValidationError(code, message string) *AppError {
	return NewError(ErrorTypeValidation, code, message)
}

// IsErrorType 检查错误是否为指定类型
func IsErrorType(err error, errorType ErrorType) bool {
	if appErr, ok := err.(*AppError); ok {
		return appErr.Type == errorType
	}
	return false
}

// IsErrorCode 检查错误是否为指定代码
func IsErrorCode(err error, code string) bool {
	if appErr, ok := err.(*AppError); ok {
		return appErr.Code == code
	}
	return false
}

// GetErrorDetails 获取错误详细信息
func GetErrorDetails(err error) map[string]interface{} {
	details := make(map[string]interface{})
	
	if appErr, ok := err.(*AppError); ok {
		details["type"] = string(appErr.Type)
		details["code"] = appErr.Code
		details["message"] = appErr.Message
		details["location"] = appErr.Location
		
		if appErr.Details != "" {
			details["details"] = appErr.Details
		}
		
		if appErr.Cause != nil {
			details["cause"] = appErr.Cause.Error()
		}
	} else {
		details["type"] = "UNKNOWN"
		details["message"] = err.Error()
	}
	
	return details
}
