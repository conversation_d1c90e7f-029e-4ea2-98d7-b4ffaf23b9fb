package sync

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"

	"api2mysql/api"
	"api2mysql/config"
	"api2mysql/database"
)

// Syncer 数据同步器
type Syncer struct {
	config    *config.Config
	dbManager *database.MySQLManager
	apiClient *api.Client
	logger    *logrus.Logger
	ctx       context.Context
	cancel    context.CancelFunc
	wg        sync.WaitGroup
}

// NewSyncer 创建数据同步器
func NewSyncer(cfg *config.Config, logger *logrus.Logger) (*Syncer, error) {
	// 创建数据库管理器
	dbManager, err := database.NewMySQLManager(&cfg.Database, logger)
	if err != nil {
		return nil, fmt.Errorf("创建数据库管理器失败: %v", err)
	}

	// 创建API客户端
	apiClient := api.NewClient(
		cfg.Global.GetRequestTimeout(),
		cfg.Global.RetryCount,
		logger,
	)

	ctx, cancel := context.WithCancel(context.Background())

	return &Syncer{
		config:    cfg,
		dbManager: dbManager,
		apiClient: apiClient,
		logger:    logger,
		ctx:       ctx,
		cancel:    cancel,
	}, nil
}

// Start 启动数据同步
func (s *Syncer) Start() error {
	s.logger.Info("启动数据同步服务")

	// 初始化所有表
	for _, apiConfig := range s.config.APIs {
		if !apiConfig.Enabled {
			s.logger.Infof("跳过已禁用的API: %s", apiConfig.Name)
			continue
		}

		err := s.dbManager.CreateTableIfNotExists(apiConfig.TableName, apiConfig.FieldTypes, apiConfig.FieldComments)
		if err != nil {
			return fmt.Errorf("初始化表 %s 失败: %v", apiConfig.TableName, err)
		}
	}

	// 启动每个API的同步任务
	for _, apiConfig := range s.config.APIs {
		if !apiConfig.Enabled {
			continue
		}

		s.wg.Add(1)
		go s.syncAPIData(apiConfig)
	}

	s.logger.Info("数据同步服务已启动")
	return nil
}

// Stop 停止数据同步
func (s *Syncer) Stop() {
	s.logger.Info("停止数据同步服务")
	s.cancel()
	s.wg.Wait()
	s.dbManager.Close()
	s.logger.Info("数据同步服务已停止")
}

// syncAPIData 同步单个API的数据
func (s *Syncer) syncAPIData(apiConfig config.APIConfig) {
	defer s.wg.Done()

	ticker := time.NewTicker(apiConfig.GetSyncInterval())
	defer ticker.Stop()

	s.logger.Infof("启动API同步任务: %s，同步间隔: %v", apiConfig.Name, apiConfig.GetSyncInterval())

	// 立即执行一次同步
	s.performSync(&apiConfig)

	// 定时同步
	for {
		select {
		case <-s.ctx.Done():
			s.logger.Infof("API同步任务停止: %s", apiConfig.Name)
			return
		case <-ticker.C:
			s.performSync(&apiConfig)
		}
	}
}

// performSync 执行同步操作
func (s *Syncer) performSync(apiConfig *config.APIConfig) {
	startTime := time.Now()
	s.logger.Infof("开始同步API数据: %s", apiConfig.Name)

	// 0. 确保表存在（防止表在运行时被删除）
	err := s.dbManager.CreateTableIfNotExists(apiConfig.TableName, apiConfig.FieldTypes, apiConfig.FieldComments)
	if err != nil {
		s.logger.Errorf("确保表存在失败 [%s]: %v", apiConfig.Name, err)
		return
	}

	// 1. 获取API数据
	rawData, err := s.apiClient.FetchData(apiConfig)
	if err != nil {
		s.logger.Errorf("获取API数据失败 [%s]: %v", apiConfig.Name, err)
		return
	}

	if len(rawData) == 0 {
		s.logger.Warnf("API返回空数据 [%s]", apiConfig.Name)
		return
	}

	// 2. 转换数据格式
	transformedData, err := s.apiClient.TransformData(rawData, apiConfig.FieldMapping)
	if err != nil {
		s.logger.Errorf("数据转换失败 [%s]: %v", apiConfig.Name, err)
		return
	}

	if len(transformedData) == 0 {
		s.logger.Warnf("数据转换后为空 [%s]", apiConfig.Name)
		return
	}

	// 3. 根据同步模式处理数据
	err = s.syncDataToDatabase(apiConfig, transformedData)
	if err != nil {
		s.logger.Errorf("数据同步到数据库失败 [%s]: %v", apiConfig.Name, err)
		return
	}

	duration := time.Since(startTime)
	s.logger.Infof("API数据同步完成: %s，耗时: %v，记录数: %d",
		apiConfig.Name, duration, len(transformedData))
}

// syncDataToDatabase 将数据同步到数据库
func (s *Syncer) syncDataToDatabase(apiConfig *config.APIConfig, data []map[string]interface{}) error {
	switch s.config.Global.SyncMode {
	case "replace":
		return s.replaceData(apiConfig.TableName, data)
	case "append":
		return s.appendData(apiConfig.TableName, data)
	case "upsert":
		return s.upsertData(apiConfig, data)
	default:
		return fmt.Errorf("不支持的同步模式: %s", s.config.Global.SyncMode)
	}
}

// replaceData 替换模式：清空表后插入新数据
func (s *Syncer) replaceData(tableName string, data []map[string]interface{}) error {
	// 清空表
	err := s.dbManager.ClearTable(tableName)
	if err != nil {
		return fmt.Errorf("清空表失败: %v", err)
	}

	// 插入新数据
	return s.dbManager.InsertData(tableName, data)
}

// appendData 追加模式：直接插入新数据
func (s *Syncer) appendData(tableName string, data []map[string]interface{}) error {
	return s.dbManager.InsertData(tableName, data)
}

// upsertData 更新插入模式：存在则更新，不存在则插入
func (s *Syncer) upsertData(apiConfig *config.APIConfig, data []map[string]interface{}) error {
	// 查找主键字段
	primaryKey := s.findPrimaryKey(apiConfig.FieldTypes)
	if primaryKey == "" {
		return fmt.Errorf("未找到主键字段，无法执行upsert操作")
	}

	return s.dbManager.UpsertData(apiConfig.TableName, data, primaryKey)
}

// findPrimaryKey 查找主键字段
func (s *Syncer) findPrimaryKey(fieldTypes map[string]string) string {
	for fieldName, fieldType := range fieldTypes {
		if contains(fieldType, "PRIMARY KEY") {
			return fieldName
		}
	}
	return ""
}

// contains 检查字符串是否包含子字符串（忽略大小写）
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			len(s) > len(substr) &&
				(s[:len(substr)] == substr ||
					s[len(s)-len(substr):] == substr ||
					indexOf(s, substr) >= 0))
}

// indexOf 查找子字符串位置
func indexOf(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// SyncOnce 执行一次性同步（用于测试或手动触发）
func (s *Syncer) SyncOnce(apiName string) error {
	for _, apiConfig := range s.config.APIs {
		if apiConfig.Name == apiName && apiConfig.Enabled {
			// 确保表存在
			err := s.dbManager.CreateTableIfNotExists(apiConfig.TableName, apiConfig.FieldTypes, apiConfig.FieldComments)
			if err != nil {
				return fmt.Errorf("初始化表 %s 失败: %v", apiConfig.TableName, err)
			}

			s.performSync(&apiConfig)
			return nil
		}
	}
	return fmt.Errorf("未找到启用的API配置: %s", apiName)
}

// GetSyncStatus 获取同步状态
func (s *Syncer) GetSyncStatus() map[string]interface{} {
	status := make(map[string]interface{})

	var enabledAPIs []string
	for _, apiConfig := range s.config.APIs {
		if apiConfig.Enabled {
			enabledAPIs = append(enabledAPIs, apiConfig.Name)
		}
	}

	status["enabled_apis"] = enabledAPIs
	status["sync_mode"] = s.config.Global.SyncMode
	status["retry_count"] = s.config.Global.RetryCount
	status["request_timeout"] = s.config.Global.RequestTimeout

	return status
}
